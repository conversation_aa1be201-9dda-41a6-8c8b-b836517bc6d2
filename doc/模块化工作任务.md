模块化工作任务

我有Files Logging Messaging Rbac DynamicForm 五个模块 后续还会增加相关模块

除以上五个模块外我还有一个Itmctr的domain 他是业务站点的主入口 以上的五个模块应该在此使用

目前现状这五个模块都是独立的domain 每个domain一般会有两个站点 WebApi和WebApi.Mgt 一个是常规类功能 而另一个是管理类功能

多个domain之间会使用httpClient进行互相访问 比如Rbac中会使用MessagingApplicationClient访问Messaging模块的非Mgt站带你

每个站点都有一个Wrapper 继承WebApiMgtWrapper或者WebApiWrapper

每个站点都会使用 await builder.InitMgt<WebApiAuthProvider, WebApiAuthInfoGetter, ItmctrWebApiMgtWrapper>().RunAsync(); 或者await builder.Init<WebApiAuthProvider, WebApiAuthInfoGetter, ItmctrWebApiMgtWrapper>().RunAsync(); 方法进行初始化注入 只不过范型不同

每个站点都需要两个范型的注入。TAuthProvider, TAuthInfoGetter  而TAuthProvider有两个实现类RbacAuthProvider和WebApiAuthProvider 其中其中Rbac的非Mgt站点使用RbacAuthProvider 从数据库查询用户信息以及权限进行返回。而其他站点均使用WebApiAuthProvider 通过UserApiClient 基于HttpClient调用接口访问Rbac的非mgt接口得到用户信息和权限结果

所有的类库或站点都引用了/Users/<USER>/Documents/project/XJFramework-ADO/Baseline/backend/Common.props和 /Users/<USER>/Documents/project/XJFramework-ADO/Baseline/backend/Common.Secrets.props 同时使用了中央包注册管理 相关文件在/Users/<USER>/Documents/project/XJFramework-ADO/Baseline/backend/Directory.Packages.props   包括也做了一些条件设置 保存在/Users/<USER>/Documents/project/XJFramework-ADO/Baseline/backend/Directory.Build.props

所有的ApiClient会分为多种 有Mgt的ApiClient 有非Mgt的ApiClient 而每个管理测也有可能有ApplicationClient和非ApplicationClient 二者区别是ApplicationClient使用参数加密生成密钥后提交给接口进行鉴权验证 鉴权时只验证签名是否正确 而非ApplicationClient使用jwt提交给接口进行鉴权  鉴权时验证用户是否存在 用户是否有对应权限

在使用ApiClient时 Application 或者Mgt的Client不一定会按站点的能力区分也就是说 有可能在Mgt的站点中使用非Mgt的ApiClient 也可能在非Mgt的站点中使用Mgt的ApiClient

同一个模块下Mgt和非Mgt的站点中接口的路由有可能会重复 但他们的作用并不同

目前我的现状是大概有11个站点用来分布这些接口(有的有Mgt接口 有的没有)  我希望对以上提到的几个部分进行模块化抽象 用来简化部署和启动 比如说我可以简化成2个站点 一个非Mgt的 一个Mgt的 或者单独在提取出Rbac的非Mgt站点作为登录服务使用  整体上我希望他是灵活的

目前的代码是Baseline 相当于一组基础模块的基线版本 后续我会创建多个站点来引用这里的模块和项目(比如shared层) 然后按需引入模块 比如某个站点只会有Files和Messaging 没有DynamicForm  后续引入时项目和模块时我会先将baseline中的代码推送到nuget或者ADO的nuget中 然后外部项目通过包管理器进行引用

为了方便调试 我会在baseline源码中新建一个example的domain进行集成测试使用 它会分为两个站点 Mgt和非Mgt

在Directory.Build.props中我编写了一些CopyToOutputDirectory命令 将settings目录中的配置文件拷贝到当前站点的目录中 他们使用AddSolutionJsonFile方法进行了配置注入 我希望外部项目在引用时可以兼容该逻辑 但settings文件是在自己的工程目录中 实际使用的配置也应该是该工程的配置而不是baseline目录中的配置

关于每个模块打包时我希望将Mgt站点和非Mgt站点一起打包进来 在引入模块的站点中可以直接使用

这里有一个难点 如果多个模块聚合在一起后以前ApiClient的代码我希望可以无缝使用 这就意味着我需要一种方式解决互相访问的问题,之前我做过一版设计和开发 但实现的并不完成,但他参考了TestServer的设计思路 将站点在内存中直接调用 以下是关键代码 他根据路由进行了匹配 然后找到对应的controller和action进行执行 当前这里有些代码实现的方式可能不合理 或者不正确 我们在重构时也需要考虑这部分的内容

