
// global using 指令

// System 命名空间
global using System;
global using System.Collections.Generic;

// XJ.Framework 基础库
global using XJ.Framework.Library.Application.Contract.Interfaces;
global using XJ.Framework.Library.Application.Contract.OperationDtos;
global using XJ.Framework.Library.Application.Contract.QueryCriteria;
global using XJ.Framework.Library.Application.Contract.QueryCriteria.Attributes;

// XJ.Framework Rbac 模块
global using XJ.Framework.Messaging.Application.Contract.OperationDtos;
global using XJ.Framework.Messaging.Application.Contract.QueryCriteria;
global using XJ.Framework.Messaging.Domain.Shared.Dtos; 