FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
WORKDIR /src

# 复制解决方案文件
COPY ["Common.props", "./"]
COPY ["Common.Secrets.props", "./"]
COPY ["Directory.Build.props", "./"]
COPY ["Directory.Packages.props", "./"]

# 复制项目文件
COPY ["src/domains/messaging/XJ.Framework.Messaging.WebApi/XJ.Framework.Messaging.WebApi.csproj", "src/domains/messaging/XJ.Framework.Messaging.WebApi/"]
# 如有依赖项目，按需补充COPY
COPY ["src/shared/XJ.Framework.Library.WebApi/XJ.Framework.Library.WebApi.csproj", "src/shared/XJ.Framework.Library.WebApi/"]
COPY ["src/shared/XJ.Framework.Library.Application.Contract/XJ.Framework.Library.Application.Contract.csproj", "src/shared/XJ.Framework.Library.Application.Contract/"]
COPY ["src/shared/XJ.Framework.Library.Domain.Shared/XJ.Framework.Library.Domain.Shared.csproj", "src/shared/XJ.Framework.Library.Domain.Shared/"]

# 复制配置文件
COPY ["settings/", "settings/"]

# 复制所有源代码
COPY ["src/", "src/"]

# 还原和构建
RUN dotnet restore "src/domains/messaging/XJ.Framework.Messaging.WebApi/XJ.Framework.Messaging.WebApi.csproj"
RUN dotnet build "src/domains/messaging/XJ.Framework.Messaging.WebApi/XJ.Framework.Messaging.WebApi.csproj" -c Release -o /app/build

FROM build AS publish
RUN dotnet publish "src/domains/messaging/XJ.Framework.Messaging.WebApi/XJ.Framework.Messaging.WebApi.csproj" -c Release -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
COPY --from=build /src/settings /app/settings
ENTRYPOINT ["dotnet", "XJ.Framework.Messaging.WebApi.dll"] 