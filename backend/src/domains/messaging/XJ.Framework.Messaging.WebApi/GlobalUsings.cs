

global using XJ.Framework.Library.Application.Extensions;
global using XJ.Framework.Library.WebApi;
global using XJ.Framework.Library.WebApi.Controllers;
global using XJ.Framework.Library.WebApi.Extensions;

global using Microsoft.AspNetCore.Mvc;
global using XJ.Framework.Library.Application.Contract.QueryCriteria;
global using XJ.Framework.Library.Application.Services;
global using XJ.Framework.Library.Domain.Attributes;
global using XJ.Framework.Library.Domain.Shared.Dtos;
global using XJ.Framework.Library.WebApi.Services;
global using XJ.Framework.Rbac.ApiClient;

global using XJ.Framework.Messaging.Application;
global using XJ.Framework.Messaging.Application.Contract.Interfaces;
global using XJ.Framework.Messaging.Application.Contract.OperationDtos;
global using XJ.Framework.Messaging.Application.Contract.QueryCriteria;
global using XJ.Framework.Messaging.Domain.Shared.Dtos;
global using XJ.Framework.Messaging.EntityFrameworkCore;
global using XJ.Framework.Messaging.WebApi;