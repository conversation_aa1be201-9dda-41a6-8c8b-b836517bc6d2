using XJ.Framework.Library.WebApi.Attributes;
using XJ.Framework.Messaging.Domain.Shared.Dtos;

namespace XJ.Framework.Messaging.WebApi.Controllers;

/// <summary>
/// MessageSend 控制器
/// </summary>
public class
    MessageSendController : BaseAppController<long, MessageSendDto, IMessageSendService, MessageSendQueryCriteria>
{
    public MessageSendController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <summary>
    /// 发送消息
    /// </summary>
    /// <param name="request">消息发送请求</param>
    /// <returns>发送结果</returns>
    [HttpPost("send")]
    [ApplicationPermission]
    public async Task<MessageSendResultDto> SendMessageAsync([FromBody] MessageSendRequestDto request)
    {
        var appCode = HttpContext.Request.Headers["x-application-code"].FirstOrDefault()!;
        return await Service.SendMessageAsync(appCode, request);
    }

    /// <summary>
    /// 获取发送状态
    /// </summary>
    /// <param name="sendId">发送记录ID</param>
    /// <returns>发送状态</returns>
    [ApplicationPermission]
    [HttpGet("status/{sendId:long}")]
    public async Task<MessageSendResultDto?> GetSendStatusAsync(long sendId)
    {
        return await Service.GetSendStatusAsync(sendId);
    }
}
