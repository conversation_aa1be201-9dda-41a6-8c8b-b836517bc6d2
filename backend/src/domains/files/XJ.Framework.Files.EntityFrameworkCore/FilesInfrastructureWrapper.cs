namespace XJ.Framework.Files.EntityFrameworkCore;

public class FilesInfrastructureWrapper : InfrastructureWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        services.AddDbContext<FilesDbContext>(
            optionsAction: (serviceProvider, contextOptions) =>
            {
                var env = serviceProvider.GetRequiredService<IWebHostEnvironment>();
                if (env.IsDevelopment())
                {
                    contextOptions.EnableSensitiveDataLogging();
                }

                contextOptions.UseQueryTrackingBehavior(QueryTrackingBehavior.NoTracking);
            },
            contextLifetime:
            ServiceLifetime.Scoped
        );

        services.AddScoped<IUnitOfWork, UnitOfWork<FilesDbContext>>();
    }
} 