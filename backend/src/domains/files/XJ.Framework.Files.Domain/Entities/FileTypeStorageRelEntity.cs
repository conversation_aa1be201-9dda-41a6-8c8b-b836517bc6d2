using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Files.Domain.Entities;

/// <summary>
/// FileTypeStorageRel 实体
/// </summary>
[Table("file_type_storage_rels", Schema = "f")]
[SoftDeleteIndex("IX_file_type_storage_rel_file_type_code", nameof(FileTypeCode), nameof(Priority))]
[SoftDeleteIndex("IX_file_type_storage_rel_storage_code", nameof(StorageCode))]
public class FileTypeStorageRelEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 文件类型code
    /// </summary>
    [Column("file_type_code")]
    public required string FileTypeCode { get; set; }

    /// <summary>
    /// 存储信息code
    /// </summary>
    [Column("storage_code")]
    public required string StorageCode { get; set; }

    /// <summary>
    /// 优先级，数字越小优先级越高
    /// </summary>
    [Column("priority")]
    public required int Priority { get; set; }
}