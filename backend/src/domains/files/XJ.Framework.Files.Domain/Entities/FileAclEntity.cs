using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Files.Domain.Entities;

/// <summary>
/// FileAcl 实体
/// </summary>
[Table("file_acls", Schema = "f")]
[SoftDeleteIndex("IX_file_acl_file_id", nameof(FileId))]
[SoftDeleteIndex("IX_file_acl_principal", [nameof(PrincipalType), nameof(PrincipalId), nameof(ExpireAt)],
    [nameof(FileId)])]
public class FileAclEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 文件ID
    /// </summary>
    [Column("file_id")]
    public required long FileId { get; set; }

    /// <summary>
    /// 主体类型（user、role、token等）
    /// </summary>
    [Column("principal_type")]
    [StringLength(64)]
    public required string PrincipalType { get; set; } = null!;

    /// <summary>
    /// 主体ID
    /// </summary>
    [Column("principal_id")]
    [StringLength(256)]
    public required string PrincipalId { get; set; } = null!;

    /// <summary>
    /// 过期时间
    /// </summary>
    [Column("expire_at")]
    public DateTime? ExpireAt { get; set; }
}