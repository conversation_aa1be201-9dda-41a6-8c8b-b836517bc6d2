using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Files.Domain.Entities;

/// <summary>
/// FileType 实体
/// </summary>
[Table("file_types", Schema = "f")]
[SoftDeleteIndex("IX_file_types_type_code", nameof(TypeCode), IsUnique = true)]
public class FileTypeEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 类型编码
    /// </summary>
    [Column("type_code")]
    [StringLength(64)]
    public required string TypeCode { get; set; } = null!;

    /// <summary>
    /// 类型名称
    /// </summary>
    [Column("type_name")]
    [StringLength(128)]
    public required string TypeName { get; set; } = null!;

    /// <summary>
    /// 允许的文件后缀（如.jpg,.png)
    /// </summary>
    [Column("extensions")]
    [StringLength(510)]
    public required string Extension { get; set; } = null!;

    /// <summary>
    /// 大小限制（字节）
    /// </summary>
    [Column("size_limit")]
    public required long SizeLimit { get; set; }

    /// <summary>
    /// 分块大小限制（字节）
    /// </summary>
    [Column("chunk_limit")]
    public long ChunkLimit { get; set; }

    /// <summary>
    /// 备注
    /// </summary>
    [Column("remark")]
    [StringLength(510)]
    public string? Remark { get; set; }

    /// <summary>
    /// 所需权限类型 none=无 user=用户 role=角色
    /// </summary>
    [StringLength(50)]
    public required string PermissionRequired { get; set; } = null!;
}