
namespace XJ.Framework.Files.Application.Services;

/// <summary>
/// FileTypeStorageRel 服务实现
/// </summary>
public sealed class FileTypeStorageRelService :
    BaseEditableAppService<long, FileTypeStorageRelEntity, FileTypeStorageRelDto, FileTypeStorageRelOperationDto, IFileTypeStorageRelRepository, FileTypeStorageRelQueryCriteria>,
    IFileTypeStorageRelService
{
    public FileTypeStorageRelService(IFileTypeStorageRelRepository repository, IMapper mapper, IUnitOfWork unitOfWork,IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }
} 