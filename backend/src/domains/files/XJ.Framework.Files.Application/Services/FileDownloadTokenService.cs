using Microsoft.IdentityModel.Tokens;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;
using XJ.Framework.Library.Cache.Abstraction;

namespace XJ.Framework.Files.Application.Services;

public class FileDownloadTokenService : IFileDownloadTokenService
{
    private readonly ICache _cache;
    private readonly IConfiguration _configuration;
    private readonly TimeSpan _tokenExpiration;

    public FileDownloadTokenService(ICache cache, IConfiguration configuration)
    {
        _cache = cache;
        _configuration = configuration;
        _tokenExpiration = TimeSpan.FromMinutes(Convert.ToInt32(_configuration["DownloadJwt:TokenExpiration"]));
    }

    public async Task<string> GenerateTokenAsync(Guid fileId, long userId)
    {
        // 创建一个随机令牌
        var tokenId = Guid.NewGuid().ToString();

        // 创建JWT令牌
        var tokenHandler = new JwtSecurityTokenHandler();
        var key = Encoding.ASCII.GetBytes(_configuration["DownloadJwt:Key"]!);

        var claims = new[]
        {
            new Claim("fileId", fileId.ToString()),
            new Claim("userId", userId.ToString()),
            new Claim(JwtRegisteredClaimNames.Jti, tokenId)
        };

        var tokenDescriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(claims),
            Issuer = _configuration["DownloadJwt:Issuer"],
            Audience = _configuration["DownloadJwt:Audience"],
            Expires = DateTime.UtcNow.Add(_tokenExpiration),
            SigningCredentials = new SigningCredentials(
                new SymmetricSecurityKey(key),
                SecurityAlgorithms.HmacSha256Signature)
        };

        var token = tokenHandler.CreateToken(tokenDescriptor);
        var tokenString = tokenHandler.WriteToken(token);

        await _cache.SetAsync(tokenId, new DownloadTokenInfo
        {
            FileId = fileId,
            UserId = userId,
            ExpireTime = DateTime.UtcNow.Add(_tokenExpiration)
        }, _tokenExpiration);

        return tokenString;
    }

    public async Task<DownloadTokenInfo?> ValidateTokenAsync(string token)
    {
        try
        {
            var tokenHandler = new JwtSecurityTokenHandler();
            var key = Encoding.ASCII.GetBytes(_configuration["DownloadJwt:Key"]!);

            // 验证令牌
            tokenHandler.ValidateToken(token, new TokenValidationParameters
            {
                ValidIssuer = _configuration["DownloadJwt:Issuer"],
                ValidAudience = _configuration["DownloadJwt:Audience"],
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = true,
                ValidateAudience = true,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.Zero
            }, out SecurityToken validatedToken);

            var jwtToken = (JwtSecurityToken)validatedToken;

            // 获取令牌ID
            var tokenId = jwtToken.Claims.First(x => x.Type == JwtRegisteredClaimNames.Jti).Value;

            // 从缓存中获取令牌信息
            var tokenInfo = await _cache.GetAsync<DownloadTokenInfo>(tokenId);
            if (tokenInfo != null)
            {
                return tokenInfo;
            }
        }
        catch
        {
            // 令牌验证失败
            return null;
        }

        return null;
    }
}