
global using XJ.Framework.Library.Application.Extensions;
global using XJ.Framework.Library.WebApi;
global using XJ.Framework.Library.WebApi.Controllers;

global using Microsoft.AspNetCore.Mvc;
global using XJ.Framework.Library.Application.Contract.QueryCriteria;
global using XJ.Framework.Library.Domain.Shared.Dtos;
global using XJ.Framework.Library.WebApi.Extensions;
global using XJ.Framework.Library.Application.Services;
global using XJ.Framework.Library.Domain.Attributes;
global using XJ.Framework.Library.WebApi.Services;

global using XJ.Framework.Files.Application.Contract.Interfaces;
global using XJ.Framework.Files.Application.Contract.OperationDtos;
global using XJ.Framework.Files.Application.Contract.QueryCriteria;
global using XJ.Framework.Files.Domain.Shared.Dtos;
global using XJ.Framework.Files.Application;
global using XJ.Framework.Files.EntityFrameworkCore;
global using XJ.Framework.Files.WebApi;