namespace XJ.Framework.DynamicForm.WebApi;

public class DynamicFormWebApiWrapper : WebApiWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        services
            .InitApplication<DynamicFormApplicationWrapper, DynamicFormInfrastructureWrapper>(configuration);
        services.AddHttpClient<UserApiClient>();
    }

    public override void UseMiddleware(WebApplication app)
    {
    }

    public override void AddFilters(MvcOptions mvcOptions)
    {
    }
}