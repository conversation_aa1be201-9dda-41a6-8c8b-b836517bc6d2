using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Options;
using System.Text.Json;
using XJ.Framework.Library.WebApi.Attributes;

namespace XJ.Framework.DynamicForm.WebApi.Controllers;

/// <summary>
/// Form 控制器
/// </summary>
public class
    FormController : BaseEditableAppController<long, FormDto, FormOperationDto, IFormService, FormQueryCriteria>
{
    private JsonSerializerOptions _jsonSerializerOptions;

    public FormController(IServiceProvider serviceProvider, IOptions<JsonOptions> jsonOptions) : base(serviceProvider)
    {
        _jsonSerializerOptions = jsonOptions.Value.JsonSerializerOptions;
    }

    [AllowAnonymous]
    [HttpGet("definition/{formCode}")]
    [IgnoreLogging]
    public async Task<FormDefinitionDto> GetDefinitionAsync(string formCode)
    {
        return await Service.LoadDefinitionAsync(formCode);
    }
}
