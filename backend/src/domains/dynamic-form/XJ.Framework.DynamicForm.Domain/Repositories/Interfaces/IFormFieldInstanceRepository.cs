using System.Linq.Expressions;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;
using XJ.Framework.Library.Domain.Common;

namespace XJ.Framework.DynamicForm.Domain.Repositories.Interfaces;

/// <summary>
/// FormFieldInstance 仓储接口
/// </summary>
public interface IFormFieldInstanceRepository : IAuditRepository<long, FormFieldInstanceEntity>
{
    Task<List<FormFieldInstanceEntity>> GetListByInstanceAsync(List<string> columns, string formVersion,
        string formCode,
        IEnumerable<(string businessId, string version)> instances);
}
