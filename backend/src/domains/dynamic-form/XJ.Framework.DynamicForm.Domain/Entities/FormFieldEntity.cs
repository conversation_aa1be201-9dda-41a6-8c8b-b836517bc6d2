using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.DynamicForm.Domain.Entities;

/// <summary>
/// FormField 实体
/// </summary>
[Table("form_fields", Schema = "d")]
[SoftDeleteIndex("UX_form_fields_form_code_version_code_parent_code_not_deleted", nameof(FormCode), nameof(Code),
    nameof(Version),
    nameof(ParentCode),
    IsUnique = true)]
[SoftDeleteIndex("IX_form_fields_form_id_not_deleted", nameof(FormCode))]
public class FormFieldEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 表单ID，关联[d].[forms].[code]
    /// </summary>
    [Column("form_code")]
    [StringLength(50)]
    public required string FormCode { get; set; }

    /// <summary>
    /// 表单版本
    /// </summary>
    [Column("version")]
    [StringLength(50)]
    public required string Version { get; set; }

    /// <summary>
    /// 父字段编码，支持子表单结构，根字段为NULL
    /// </summary>
    [Column("parent_code")]
    [StringLength(50)]
    public string? ParentCode { get; set; }

    /// <summary>
    /// 分组编码，关联[d].[form_field_groups].[code]，可为空
    /// </summary>
    [Column("group_code")]
    [StringLength(50)]
    public required string GroupCode { get; set; }

    /// <summary>
    /// 字段唯一编码，同一form_id下唯一
    /// </summary>
    [Column("code")]
    [StringLength(50)]
    public required string Code { get; set; } = null!;

    /// <summary>
    /// 中文标签
    /// </summary>
    [Column("label_zh")]
    [StringLength(400)]
    public string? LabelZh { get; set; }

    /// <summary>
    /// 英文标签
    /// </summary>
    [Column("label_en")]
    [StringLength(400)]
    public string? LabelEn { get; set; }

    /// <summary>
    /// 控件类型，可选值：text（单行文本）、text_multilang（双语单行文本）、textarea（多行文本）、textarea_multilang（双语多行文本）、select（下拉）、checkbox（多选）、radio（单选）、date（日期）、number（数字）、unit_select（数字+单位下拉）、int_range（整数范围）、date_range（日期范围）、form_single（单行子表单）、form_multi（多行子表单）
    /// </summary>
    [Column("type")]
    [StringLength(50)]
    public required string Type { get; set; } = null!;

    /// <summary>
    /// 是否必填，0-否，1-是
    /// </summary>
    [Column("required")]
    public required bool Required { get; set; }

    /// <summary>
    /// 是否新起一行，0-否，1-是
    /// </summary>
    [Column("newLine")]
    public required bool NewLine { get; set; }

    /// <summary>
    /// 选项（json），type为select/checkbox/radio/unit_select等有选项的控件时使用，支持中英文双语。\nselect/checkbox/radio格式如：[{'label_zh':'选项1', 'label_en':'Option 1', 'value':'1'}]；\nunit_select格式如：[{'label_zh':'天', 'label_en':'Day', 'value':'day'}]；\nint_range格式如：{"min":0, "max":100, "step":1}；\ndate_range格式如：{"min":"2020-01-01", "max":"2030-12-31"}
    /// </summary>
    [Column("options")]
    [StringLength(-1)]
    public string? Option { get; set; }

    /// <summary>
    /// 默认值，类型与控件type对应。\nselect/checkbox/radio为选项value，checkbox为数组；\nunit_select为{"value":1,"unit":"day"}；\nint_range为{"min":10, "max":20}；\ndate_range为{"start":"2023-01-01", "end":"2023-12-31"}；\ntext_multilang/textarea_multilang为{"zh":"中文值", "en":"英文值"}
    /// </summary>
    [Column("default_value")]
    [StringLength(-1)]
    public string? DefaultValue { get; set; }

    /// <summary>
    /// 控件占用列数，默认1，范围1-3
    /// </summary>
    [Column("colspan")]
    public required int Colspan { get; set; }

    /// <summary>
    /// 排序，正序排列
    /// </summary>
    [Column("sort_order")]
    public required int SortOrder { get; set; }

    /// <summary>
    /// 额外配置（json），如校验规则、动态行为等
    /// </summary>
    [Column("extra_config")]
    [StringLength(-1)]
    public string? ExtraConfig { get; set; }

    /// <summary>
    /// 字段备注
    /// </summary>
    [Column("description")]
    [StringLength(800)]
    public string? Description { get; set; }

    /// <summary>
    /// 字段驳回时的批注信息
    /// </summary>
    [StringLength(2000)]
    [Column("reject_reason")]
    public string? RejectReason { get; set; }
}
