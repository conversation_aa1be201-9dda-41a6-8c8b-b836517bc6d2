using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.DynamicForm.Domain.Entities;

/// <summary>
/// Form 实体
/// </summary>
[Table("forms", Schema = "d")]
[SoftDeleteIndex("UX_forms_code_version_not_deleted", nameof(Code), nameof(Version), IsUnique = true)]
public class FormEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 表单唯一编码
    /// </summary>
    [Column("code")]
    [StringLength(50)]
    public required string Code { get; set; } = null!;

    /// <summary>
    /// 表单名称
    /// </summary>
    [Column("name")]
    [StringLength(400)]
    public required string Name { get; set; } = null!;

    /// <summary>
    /// 表单版本
    /// </summary>
    [Column("version")]
    [StringLength(40)]
    public required string Version { get; set; } = null!;

    /// <summary>
    /// 表单描述
    /// </summary>
    [Column("description")]
    [StringLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// 全局配置（json）
    /// </summary>
    [Column("json_config")]
    [StringLength(-1)]
    public string? JsonConfig { get; set; }
    
    
    /// <summary>
    /// 是否最新版本
    /// </summary>
    [Column("newest_version")]
    public bool NewestVersion { get; set; }
}
