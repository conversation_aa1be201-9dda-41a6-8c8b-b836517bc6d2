using System.Collections.Specialized;
using System.Text;
using System.Text.Json;
using XJ.Framework.DynamicForm.Application.Contract.OperationDtos;
using XJ.Framework.DynamicForm.Application.Contract.QueryCriteria;
using XJ.Framework.Library.Application.Contract.QueryCriteria;

namespace XJ.Framework.Files.ApiClient;

public class DynamicFormApiClientHelper
{
    public NameValueCollection BuildCriteriaNameValueCollection(
        PagedQueryCriteria<FormInstanceQueryCriteria> criteria,
        JsonSerializerOptions jsonSerializerOptions)
    {
        var query = new NameValueCollection
        {
            { "$pageIndex", criteria.PageParams.PageIndex.ToString() },
            { "$pageSize", criteria.PageParams.PageSize.ToString() },
            { "formCode", criteria.Condition.FormCode },
            { "isObsoleted", criteria.Condition.IsObsoleted?.ToString() ?? "false" }
        };
        if (criteria.Condition.Status is { Count: > 0 })
        {
            foreach (var status in criteria.Condition.Status)
            {
                query.Add($"status[]", ((int)status).ToString());
            }
        }

        if (criteria.Condition.FormDataDynamicQueries.Any())
        {
            var index = 0;
            foreach (var formDataDynamicQuery in criteria.Condition.FormDataDynamicQueries)
            {
                query.Add($"formDataDynamicQueries[{index}][key]", formDataDynamicQuery.Key);
                query.Add($"formDataDynamicQueries[{index}][operator]",
                    ((int)formDataDynamicQuery.Operator).ToString());
                query.Add($"formDataDynamicQueries[{index}][value]", formDataDynamicQuery.Value ?? string.Empty);
                index++;
            }
        }

        if (criteria.Condition.Columns.Any())
        {
            foreach (var column in criteria.Condition.Columns)
            {
                query.Add($"columns[]", column);
            }
        }

        if (criteria.Condition.DynamicQueries.Any())
        {
            foreach (var dynamicQuery in criteria.Condition.DynamicQueries)
            {
                var values = dynamicQuery.Value.Values;
                var isOr = dynamicQuery.Value.Or;

                query.Add($"dynamicQueries[{dynamicQuery.Key}][or]", isOr.ToString().ToLower());
                var i = 0;
                foreach (var value in values)
                {
                    var jsonEscaped = JsonSerializer.Serialize(value, jsonSerializerOptions);
                    // 移除两端的引号
                    jsonEscaped = jsonEscaped[1..^1];
                    query.Add($"dynamicQueries[{dynamicQuery.Key}][values][{i}]", jsonEscaped);
                    i++;
                }
            }
        }

        if (criteria.OrderBy.Any())
        {
            foreach (var orderBy in criteria.OrderBy)
            {
                query.Add($"$sortBy", orderBy.DataField);
                query.Add($"$orderBy", orderBy.SortDirection == FieldSortDirection.Ascending ? "asc" : "desc");
            }
        }

        return query;
    }
}
