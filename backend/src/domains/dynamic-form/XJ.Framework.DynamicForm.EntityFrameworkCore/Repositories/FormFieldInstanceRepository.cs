using Microsoft.Data.SqlClient;
using System.Linq.Expressions;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.Domain.Entities;
using Microsoft.EntityFrameworkCore;
using System.Text;
using XJ.Framework.DynamicForm.Domain.Shared.Dtos;

namespace XJ.Framework.DynamicForm.EntityFrameworkCore.Repositories;

/// <summary>
/// FormFieldInstance 仓储实现
/// </summary>
public class FormFieldInstanceRepository :
    BaseSoftDeleteRepository<DynamicFormDbContext, long, FormFieldInstanceEntity>,
    IFormFieldInstanceRepository
{
    public FormFieldInstanceRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    public async Task<List<FormFieldInstanceEntity>> GetListByInstanceAsync(List<string> columns, string formVersion,
        string formCode,
        IEnumerable<(string businessId, string version)> instances)
    {
        if (!instances.Any())
            return new List<FormFieldInstanceEntity>();

        var sqlParameters = new List<SqlParameter>();
        var queryBuilder = new StringBuilder();
        instances.ForEach((idx, instance) =>
        {
            if (idx > 0) queryBuilder.AppendLine("UNION ALL");

            queryBuilder.AppendLine($@"
                            SELECT [f].*
                            FROM [d].[form_field_instances] AS [f] WITH (INDEX(IX_form_field_instances_search))
                            WHERE [f].[is_deleted] = 0 
                              AND [f].[form_code] = @formCode 
                              AND [f].[form_version] = @formVersion
                              AND [f].[business_id] = @businessId_{idx} 
                              AND [f].[version] = @version_{idx}");
            if (columns.Any())
            {
                queryBuilder.AppendLine(
                    $" AND [f].[code] IN ({string.Join(",", columns.Select(q => $"@code_{q}_{idx}"))})");
                sqlParameters.AddRange(columns.Select((q, i) => new SqlParameter($"@code_{q}_{idx}", q)));
            }

            sqlParameters.Add(new SqlParameter($"@businessId_{idx}", instance.businessId));
            sqlParameters.Add(new SqlParameter($"@version_{idx}", instance.version));
        });

        // queryBuilder.AppendLine(" OPTION (OPTIMIZE FOR UNKNOWN, RECOMPILE);");

        sqlParameters.Add(new SqlParameter("@formCode", formCode));
        sqlParameters.Add(new SqlParameter("@formVersion", formVersion));

        var result = this.DbSet.FromSqlRaw(queryBuilder.ToString(), sqlParameters.ToArray());
        return await result.ToListAsync();
    }
}
