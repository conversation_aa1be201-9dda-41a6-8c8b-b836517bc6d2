namespace XJ.Framework.DynamicForm.EntityFrameworkCore;

public class DynamicFormDbContext : BaseDbContext
{
    public DynamicFormDbContext(DbContextOptions options, IConfiguration configuration,
        IOptions<DatabaseOption> databaseOptions) : base(options, databaseOptions)
    {
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        optionsBuilder
            .UseSqlServer(DatabaseOptions.Value["DynamicForm"]!.ConnectionString, (option) =>
            {
                
            });
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Initialize();

        modelBuilder.AddPropertyComments();

        modelBuilder.ApplyIndexAttributes();

        modelBuilder.Seed();

        // 配置枚举转换
        modelBuilder.ConfigureEnumToIntConversions();
    }
}