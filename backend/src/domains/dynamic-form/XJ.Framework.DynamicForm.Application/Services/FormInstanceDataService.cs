using System.Linq.Expressions;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.DynamicFormInstanceData.Application.Services;

/// <summary>
/// FormInstanceData 服务实现
/// </summary>
public sealed class FormInstanceDataService :
    BaseEditableAppService<long, FormInstanceDataEntity, FormInstanceDataDto, FormInstanceDataOperationDto,
        IFormInstanceDataRepository, FormInstanceDataQueryCriteria>,
    IFormInstanceDataService
{
    public FormInstanceDataService(IFormInstanceDataRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository, mapper, unitOfWork,
        keyGenerator, currentUserContext)
    {
    }

    public Expression<Func<FormInstanceDataEntity, bool>> GetEqualToFormInstanceExpr(
        FormInstanceDto formInstanceDto)
    {
        return q =>
            q.BusinessId.ToLower().Equals(formInstanceDto.BusinessId.ToLower()) &&
            q.Version.ToLower().Equals(formInstanceDto.Version.ToLower()) &&
            q.FormCode.ToLower().Equals(formInstanceDto.FormCode.ToLower()) &&
            q.FormVersion.ToLower().Equals(formInstanceDto.FormVersion.ToLower());
    }

    public Expression<Func<FormInstanceDataEntity, bool>> GetIgnoreCaseEqualToFormInstanceExpr(
        FormInstanceDto formInstanceDto)
    {
        return q =>
            q.BusinessId.Equals(formInstanceDto.BusinessId) &&
            q.Version.Equals(formInstanceDto.Version) &&
            q.FormCode.Equals(formInstanceDto.FormCode) &&
            q.FormVersion.Equals(formInstanceDto.FormVersion);
    }

    public Expression<Func<FormInstanceDataDto, bool>> GetDtoEqualToFormInstanceExpr(FormInstanceDto formInstanceDto)
    {
        return q =>
            q.BusinessId.ToLower().Equals(formInstanceDto.BusinessId.ToLower()) &&
            q.Version.ToLower().Equals(formInstanceDto.Version.ToLower()) &&
            q.FormCode.ToLower().Equals(formInstanceDto.FormCode.ToLower()) &&
            q.FormVersion.ToLower().Equals(formInstanceDto.FormVersion.ToLower());
    }

    public async Task<object> QueryFormDataAsync(string queryCode, Dictionary<string, string> queryParameters)
    {
        switch (queryCode.ToLower())
        {
            case "registration_number":
            {
                var prefix = queryParameters.GetValueOrDefault("prefix", string.Empty);
                var year = queryParameters.GetValueOrDefault("year", string.Empty);

                if (string.IsNullOrEmpty(prefix) || string.IsNullOrEmpty(year))
                    throw new ArgumentException("prefix和year不能为空");

                var fullPrefix = (prefix + year).ToLower();

                // 查询所有以 prefix+year 开头的注册号
                Expression<Func<FormInstanceDataEntity, bool>> predicate =
                    q => q.Code.ToLower() == "RegistrationNumber".ToLower()
                         && q.Value != null
                         && q.Value.ToLower().StartsWith(fullPrefix);

                var data = (await Repository.GetListAsync(predicate)).ToList();

                // 提取后六位流水号
                var usedNumbers = data
                    .Select(q =>
                    {
                        var value = q.Value ?? "";
                        if (value.Length >= fullPrefix.Length + 6)
                        {
                            var numberPart = value.Substring(fullPrefix.Length, 6);
                            if (int.TryParse(numberPart, out var num))
                                return num;
                        }

                        return (int?)null;
                    })
                    .Where(x => x.HasValue)
                    .Select(x => x!.Value)
                    .OrderBy(x => x)
                    .ToList();

                // 计算可用区间
                var availableRanges = new List<(int Start, int End)>();
                int lastUsed = 0;
                foreach (var used in usedNumbers)
                {
                    if (used - lastUsed > 1)
                    {
                        availableRanges.Add((lastUsed + 1, used - 1));
                    }

                    lastUsed = used;
                }

                if (lastUsed < 999999)
                {
                    availableRanges.Add((lastUsed + 1, 999999));
                }

                // 格式化区间为字符串
                var availableNumberRanges = availableRanges
                    .Select(r => new
                    {
                        // startString = r.Start.ToString("D6"),
                        // endString = r.End.ToString("D6"),
                        start = r.Start,
                        end = r.End
                    })
                    .ToList();

                return new
                {
                    prefix,
                    year,
                    usedNumbers,
                    availableNumberRanges
                };
            }
            default:
                throw new NotSupportedException($"Query code '{queryCode}' is not supported.");
        }
    }

    public async Task<bool> ExistFormDataValueAsync(string formCode, string formDataCode, string formDataValue)
    {
        return await Repository.AnyAsync(q =>
            q.FormCode.ToLower().Equals(formCode.ToLower()) &&
            q.Code.ToLower().Equals(formDataCode.ToLower()) &&
            q.Value != null &&
            q.Value.ToLower().Equals(formDataValue.ToLower()));
    }

    public async Task<bool> ExistFormDataValueAsync(string formCode, string businessId, string formDataCode,
        string formDataValue)
    {
        return await Repository.AnyAsync(q =>
            q.FormCode.ToLower().Equals(formCode.ToLower()) &&
            q.Code.ToLower().Equals(formDataCode.ToLower()) &&
            q.Value != null &&
            !q.BusinessId.ToLower().Equals(businessId.ToLower()) &&
            q.Value.ToLower().Equals(formDataValue.ToLower()));
    }


    public async Task<List<FormInstanceDataDto>> GetByFormInstanceAsync(FormInstanceDto formInstanceDto)
    {
        var list = await Repository.GetListAsync(GetIgnoreCaseEqualToFormInstanceExpr(formInstanceDto));

        return Mapper.Map<List<FormInstanceDataDto>>(list);
    }

    public async Task<bool> AddFormDataAsync(FormInstanceDto formInstanceDto, Dictionary<string, string?> formData)
    {
        var formDataList = await Repository.GetListAsync(GetEqualToFormInstanceExpr(formInstanceDto));

        var addList = formData
            .Where(q => !formDataList.Any(f => f.Code.ToLower().Equals(q.Key.ToLower())))
            .ToList();
        if (addList.Any())
        {
            await CreateAsync(addList.Select(f => new FormInstanceDataOperationDto()
            {
                BusinessId = formInstanceDto.BusinessId,
                Version = formInstanceDto.Version,
                FormCode = formInstanceDto.FormCode,
                FormVersion = formInstanceDto.FormVersion,
                Code = f.Key,
                Value = f.Value
            }).ToList());
        }

        return true;
    }

    public async Task<bool> SetFormDataAsync(FormInstanceDto formInstanceDto, Dictionary<string, string?> formData)
    {
        var formDataList = await Repository.GetListAsync(GetEqualToFormInstanceExpr(formInstanceDto));

        var addList = formData
            .Where(q => !formDataList.Any(f => f.Code.ToLower().Equals(q.Key.ToLower())))
            .ToList();

        var editList = formDataList
            .Where(q => formData.Any(f => f.Key.ToLower().Equals(q.Code.ToLower())))
            .ToList();

        if (addList.Any())
        {
            await CreateAsync(addList.Select(f => new FormInstanceDataOperationDto()
            {
                BusinessId = formInstanceDto.BusinessId,
                Version = formInstanceDto.Version,
                FormCode = formInstanceDto.FormCode,
                FormVersion = formInstanceDto.FormVersion,
                Code = f.Key,
                Value = f.Value
            }).ToList());
        }

        if (editList.Any())
        {
            editList.ForEach(q =>
            {
                var finder = formData.FirstOrDefault(f => f.Key.ToLower().Equals(q.Code.ToLower()));
                q.Value = finder.Value ?? string.Empty;
            });
            await Repository.UpdateAsync(editList);
        }

        return true;
    }

    public async Task<List<FormInstanceDataDto>> GetListByFormInstanceAsync(string formVersion, string formCode,
        IEnumerable<FormInstanceDto> instances, List<string> columns)
    {
        var result = await Repository.GetListByInstanceAsync(columns, formVersion, formCode, instances
            .Select(q => (businessId: q.BusinessId, version: q.Version)).ToList());

        return await GetDtosAsync(result);
        // // 创建初始表达式
        // Expression<Func<FormInstanceDataEntity, bool>> predicate = q => false;
        //
        // // 为每个实例创建条件并合并
        // foreach (var instance in instances)
        // {
        //     predicate = predicate.Or(GetIgnoreCaseEqualToFormInstanceExpr(instance));
        // }
        //
        // var formFieldInstances = await Repository.GetListAsync(predicate);
        //
        // return formFieldInstances.Select(q => Mapper.Map<FormInstanceDataDto>(q)).ToList();
    }
}
