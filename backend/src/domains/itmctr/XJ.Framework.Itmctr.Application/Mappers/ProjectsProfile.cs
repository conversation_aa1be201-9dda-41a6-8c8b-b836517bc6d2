using AutoMapper;
using XJ.Framework.Itmctr.Application.Contract.OperationDtos;
using XJ.Framework.Itmctr.Domain.Entities;
using XJ.Framework.Itmctr.Domain.Shared.Dtos;

namespace XJ.Framework.Rbac.Application.Mappers;

/// <summary>
/// AsyncTask Profile
/// </summary>
public class ProjectsProfile : Profile
{
    public ProjectsProfile()
    {

        CreateMap<ProjectEntity, ProjectHistoryEntity>();
        CreateMap<ProjectHistoryEntity, ProjectEntity>();
        CreateMap<ProjectAttachEntity, ProjectAttachHistoryEntity>();
        CreateMap<ProjectAttachHistoryEntity, ProjectAttachEntity>();
        CreateMap<ProjectHumanSampleEntity, ProjectHumanSampleHistoryEntity>();
        CreateMap<ProjectHumanSampleHistoryEntity, ProjectHumanSampleEntity>();
        CreateMap<ProjectInterventionEntity, ProjectInterventionHistoryEntity>();
        CreateMap<ProjectInterventionHistoryEntity, ProjectInterventionEntity>();
        CreateMap<ProjectMeasurementEntity, ProjectMeasurementHistoryEntity>();
        CreateMap<ProjectMeasurementHistoryEntity, ProjectMeasurementEntity>();
        CreateMap<ProjectResearchSiteEntity, ProjectResearchSiteHistoryEntity>();
        CreateMap<ProjectResearchSiteHistoryEntity, ProjectResearchSiteEntity>();
        CreateMap<ProjectSponsorEntity, ProjectSponsorHistoryEntity>();
        CreateMap<ProjectSponsorHistoryEntity, ProjectSponsorEntity>();
        CreateMap<ProjectEntity, ProjectsDto>();
        CreateMap<ProjectsDto, ProjectEntity>();
        CreateMap<ProjectHumanSampleEntity, ProjectHumanSampleDto>();
        CreateMap<ProjectHumanSampleHistoryEntity, ProjectHumanSampleDto>();
        CreateMap<ProjectInterventionEntity, ProjectInterventionDto>();
        CreateMap<ProjectInterventionHistoryEntity, ProjectInterventionDto>();
        CreateMap<ProjectSponsorEntity, ProjectSponsorDto>();
        CreateMap<ProjectSponsorHistoryEntity, ProjectSponsorDto>();
        CreateMap<ProjectResearchSiteEntity, ProjectResearchSiteDto>();
        CreateMap<ProjectResearchSiteHistoryEntity, ProjectResearchSiteDto>();
        CreateMap<ProjectMeasurementEntity, ProjectMeasurementDto>();
        CreateMap<ProjectMeasurementHistoryEntity, ProjectMeasurementDto>();
        CreateMap<ProjectHistoryEntity, ProjectsDto>();
    }
}
