global using Microsoft.Extensions.Configuration;
global using Microsoft.Extensions.DependencyInjection;
global using XJ.Framework.Library.Application;

global using AutoMapper;
global using XJ.Framework.Itmctr.Application.Contract.OperationDtos;
global using XJ.Framework.Itmctr.Domain.Entities;
global using XJ.Framework.Itmctr.Domain.Shared.Dtos;


global using XJ.Framework.Itmctr.Application.Contract.Interfaces;
global using XJ.Framework.Itmctr.Application.Contract.QueryCriteria;
global using XJ.Framework.Itmctr.Domain.Repositories.Interfaces;
global using XJ.Framework.Library.Application.Services;
global using XJ.Framework.Library.Domain.Id;
global using XJ.Framework.Library.Domain.Shared.Interfaces;
global using XJ.Framework.Library.Domain.UOW;
