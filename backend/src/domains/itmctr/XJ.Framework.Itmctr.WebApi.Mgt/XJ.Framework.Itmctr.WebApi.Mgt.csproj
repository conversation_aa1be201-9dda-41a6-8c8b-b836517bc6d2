<Project Sdk="Microsoft.NET.Sdk.Web">

    <Import Project="..\..\..\..\Common.Secrets.props"/>
    <Import Project="..\..\..\..\Common.props"/>

    <ItemGroup>
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.WebApi.Mgt\XJ.Framework.Library.WebApi.Mgt.csproj" />
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.WebApi\XJ.Framework.Library.WebApi.csproj"/>
        <ProjectReference Include="..\XJ.Framework.Itmctr.Application\XJ.Framework.Itmctr.Application.csproj"/>
        <ProjectReference Include="..\XJ.Framework.Itmctr.Domain.Shared\XJ.Framework.Itmctr.Domain.Shared.csproj"/>
    </ItemGroup>

</Project> 