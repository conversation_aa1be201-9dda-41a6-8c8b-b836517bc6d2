using AutoMapper;
using System.Diagnostics;
using System.Text.Json;
using XJ.Framework.Itmctr.Application.Contract.Interfaces;
using XJ.Framework.Itmctr.Application.Contract.OperationDtos;
using XJ.Framework.Itmctr.Application.Contract.QueryCriteria;
using XJ.Framework.Itmctr.Application.Services;
using XJ.Framework.Itmctr.Domain.Entities;
using XJ.Framework.Itmctr.Domain.Repositories.Interfaces;
using XJ.Framework.Itmctr.Domain.Shared.Enums;
using XJ.Framework.Library.Common.Abstraction.Contexts;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.UOW;
using XJ.Framework.Library.Logging.Abstraction.DI;

namespace XJ.Framework.Itmctr.WebApi.Mgt.Works;

public class ProcessSubmitContentCompareService : BackgroundService
{
    private readonly ILogger<ProcessSubmitContentCompareService> _logger;
    private readonly IAsyncTaskService _asyncTaskService;
    private readonly IMapper _mapper;
    private readonly IContextContainer _contextContainer;
    private readonly IServiceScopeFactory _serviceScopeFactory;


    public ProcessSubmitContentCompareService(ILogger<ProcessSubmitContentCompareService> logger,
        IMapper mapper, IContextContainer contextContainer,
        IServiceScopeFactory serviceScopeFactory)
    {
        _serviceScopeFactory = serviceScopeFactory;


        _logger = logger;
        _mapper = mapper;
        _contextContainer = contextContainer;

        _asyncTaskService = _serviceScopeFactory.CreateScope().ServiceProvider.GetRequiredService<IAsyncTaskService>();
    }

    private static List<T>? JsonElementToObjectList<T>(object? obj)
    {
        if (obj is List<T> list)
        {
            return list;
        }

        if (obj is System.Text.Json.JsonElement je && je.ValueKind == System.Text.Json.JsonValueKind.Array)
        {
            return System.Text.Json.JsonSerializer.Deserialize<List<T>>(je.GetRawText());
        }

        return null;
    }

    protected async override Task ExecuteAsync(CancellationToken stoppingToken)
    {
        while (!stoppingToken.IsCancellationRequested)
        {
            var pending = await _asyncTaskService.GetPageAsync(new PagedQueryCriteria<AsyncTaskQueryCriteria>()
            {
                Condition = new AsyncTaskQueryCriteria
                {
                    TaskStatus = AsyncTaskStatus.Pending,
                    TaskCode = "SubmitToContentCompare"
                },
                PageParams = new PageRequestParams(1, 10)
            });

            await pending.Rows.ForEachAsync(async asyncTask =>
            {
                var asyncTaskOperationDto = _mapper.Map<AsyncTaskOperationDto>(asyncTask);

                using (var scope = _serviceScopeFactory.CreateScope())
                {
                    var asyncTaskService = scope.ServiceProvider.GetRequiredService<IAsyncTaskService>();

                    var projectService = scope.ServiceProvider.GetRequiredService<IProjectService>();

                    var unitOfWork = scope.ServiceProvider.GetRequiredService<IUnitOfWork>();

                    _contextContainer.Init();

                    var dict = JsonSerializer.Deserialize<Dictionary<string, object?>>(asyncTask.TaskData)!;

                    var businessId = dict["BusinessId"]!.ToString()!;
                    var version = dict["Version"]!.ToString()!;

                    await unitOfWork.BeginTransactionAsync(stoppingToken);
                    try
                    {
                        Stopwatch watch = new();
                        watch.Start();

                        _logger.LoggingInformation("submit-to-unicom-content-compare",
                            "开始处理提交给联通进行内容对比,BusinessId:{BusinessId},Version:{Version}", businessId, version);

                        await projectService.SubmitToContentCompareAsync(asyncTask.ApplyUserId, businessId, version);

                        asyncTaskOperationDto.TaskStatus = AsyncTaskStatus.Completed;

                        await asyncTaskService.UpdateAsync(asyncTask.Key, asyncTaskOperationDto);

                        watch.Stop();

                        _logger.LoggingInformation("submit-to-unicom-content-compare",
                            "处理提交给联通进行内容对比完成,BusinessId:{BusinessId},Version:{Version},Duration:{Duration}", businessId,
                            version, watch.Elapsed.TotalSeconds);

                        await unitOfWork.CommitAsync(stoppingToken);
                    }
                    catch (Exception ex)
                    {
                        _logger.LoggingException("submit-to-unicom-content-compare", ex,
                            "处理提交给联通进行内容对比失败,BusinessId:{BusinessId},Version:{Version}", businessId, version);

                        await unitOfWork.RollbackAsync(stoppingToken);

                        asyncTaskOperationDto.TaskStatus = AsyncTaskStatus.Failed;
                        asyncTaskOperationDto.TaskResult = ex.Message;
                    }
                }

                // 在事务外更新任务状态
                if (asyncTaskOperationDto.TaskStatus == AsyncTaskStatus.Failed)
                {
                    using (var updateScope = _serviceScopeFactory.CreateScope())
                    {
                        var updateAsyncTaskService =
                            updateScope.ServiceProvider.GetRequiredService<IAsyncTaskService>();
                        await updateAsyncTaskService.UpdateAsync(asyncTask.Key, asyncTaskOperationDto);
                    }
                }
            });

            await Task.Delay(TimeSpan.FromSeconds(5), stoppingToken);
        }
    }
}
