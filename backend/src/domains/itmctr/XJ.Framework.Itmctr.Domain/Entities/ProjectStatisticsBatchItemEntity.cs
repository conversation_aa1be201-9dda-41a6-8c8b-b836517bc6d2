namespace XJ.Framework.Itmctr.Domain.Entities;

[Table("project_statistics_batch_items", Schema = "rpt")]
public class ProjectStatisticsBatchItemEntity : BaseEntity<long>
{
    /// <summary>
    /// 业务ID
    /// </summary>
    [Column("business_id")]
    public string BusinessId { get; set; } = null!;

    /// <summary>
    /// 版本
    /// </summary>
    [Column("version")]
    public string Version { get; set; } = null!;


    /// <summary>
    /// 批次id
    /// </summary>
    [Column("batch_id")]
    public required long BatchId { get; set; }

    /// <summary>
    /// 状态 1-未处理 2-处理中 3-已处理 4-处理失败
    /// </summary>
    [Column("status")]
    public required int Status { get; set; }
}
