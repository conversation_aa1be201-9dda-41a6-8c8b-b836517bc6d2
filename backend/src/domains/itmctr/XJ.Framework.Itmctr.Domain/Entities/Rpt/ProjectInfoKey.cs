namespace XJ.Framework.Itmctr.Domain.Entities;

public class ProjectInfo<PERSON>ey
{
    public string BusinessId { get; }
    public string Version { get; }

    public ProjectInfoKey(string businessId, string version)
    {
        BusinessId = businessId;
        Version = version;
    }

    public override bool Equals(object? obj)
    {
        if (obj is ProjectInfoKey key)
        {
            return BusinessId == key.BusinessId && Version == key.Version;
        }

        return false;
    }

    public override int GetHashCode()
    {
        return HashCode.Combine(BusinessId, Version);
    }
}
