namespace XJ.Framework.Itmctr.Domain.Entities;

[Table("project_statistics_batch", Schema = "rpt")]
public class ProjectStatisticsBatchEntity : BaseEntity<long>
{
    /// <summary>
    /// 统计开始时间
    /// </summary>
    [Column("start_time")]
    public required DateTimeOffset StartTime { get; set; }

    /// <summary>
    /// 统计结束时间
    /// </summary>
    [Column("end_time")]
    public required DateTimeOffset EndTime { get; set; }

    /// <summary>
    /// 创建时间
    /// </summary>
    [Column("created_time")]
    public required DateTimeOffset CreatedTime { get; set; }

    /// <summary>
    /// 统计类型
    /// </summary>
    /// <returns></returns>
    [Column("category")]
    public required string Category { get; set; }
}
