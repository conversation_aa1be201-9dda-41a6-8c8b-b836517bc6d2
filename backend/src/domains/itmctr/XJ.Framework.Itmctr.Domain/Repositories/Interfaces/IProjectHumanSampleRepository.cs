using XJ.Framework.Itmctr.Domain.Entities;

namespace XJ.Framework.Itmctr.Domain.Repositories.Interfaces;

/// <summary>
/// 干预措施 仓储接口
/// </summary>
public interface IProjectHumanSampleRepository : IAuditRepository<long, ProjectHumanSampleEntity>
{
    Task<bool> DetachAndInsertAsync(List<ProjectHumanSampleEntity> entities);
    Task<bool> DetachAndDeleteAsync(List<ProjectHumanSampleEntity> entities);
}
