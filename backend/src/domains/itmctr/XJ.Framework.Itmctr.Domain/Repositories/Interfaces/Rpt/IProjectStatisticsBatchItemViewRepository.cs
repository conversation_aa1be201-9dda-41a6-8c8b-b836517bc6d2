using System.Linq.Expressions;
using XJ.Framework.Library.Domain.Common;

namespace XJ.Framework.Itmctr.Domain.Repositories.Interfaces;

public interface
    IProjectStatisticsBatchItemViewRepository :
    IRepository<long, ProjectStatisticsBatchItemViewEntity>
{
    new Task<PageData<long, ProjectStatisticsBatchItemViewEntity>> GetPageAsync(
        Expression<Func<ProjectStatisticsBatchItemViewEntity, bool>> where<PERSON><PERSON>b<PERSON>,
        int rowIndex, int pageSize,
        List<OrderbyDirection<ProjectStatisticsBatchItemViewEntity>> orderBy,
        bool isNoTracking = true);
}
