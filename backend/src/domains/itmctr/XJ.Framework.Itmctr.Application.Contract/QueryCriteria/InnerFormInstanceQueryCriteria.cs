using XJ.Framework.DynamicForm.Domain.Shared.Dtos;

namespace XJ.Framework.Itmctr.Application.Contract.QueryCriteria;

public class InnerFormInstanceQueryCriteria : BaseQueryCriteria
{
    public Dictionary<string, DynamicFormQueryDto> DynamicQueries { get; set; } = new();
    
    public List<string> Columns { get; set; } = new();

    public List<FormDataDynamicQuery> FormDataDynamicQueries { get; set; } = new List<FormDataDynamicQuery>();
}
