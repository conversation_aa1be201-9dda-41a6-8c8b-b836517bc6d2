using XJ.Framework.Itmctr.Domain.Entities;
using XJ.Framework.Library.Domain.Shared.Dtos;

namespace XJ.Framework.Itmctr.Application.Contract.Interfaces;

public interface IProjectStatisticsService :
    IEditableAppService<long, ProjectStatisticsBatchOperationDto>,
    IAppService<long, ProjectStatisticsBatchDto, ProjectStatisticsQueryCriteria>
{
    Task<PageDtoData<long, ProjectStatisticsBatchItemViewDto>> GetPageItemsAsync(
        long batchId, PagedQueryCriteria<ProjectStatisticsBatchItemViewQueryCriteria> criteria);

    Task<bool> RemoveItemAsync(long batchId, long itemId);
    Task<bool> AddItemAsync(long batchId, List<ProjectInfoKey> projectInfoKeys);
    Task<bool> CreateExtractProjectTaskAsync(long batchId, bool force = true, List<long>? itemIds = null);
    Task<long> CreateBatchAsync(CreateProjectStatisticsBatchRequest request);
    Task<bool> ReStatisticsAsync(long batchId);
    Task<FileDto> ExportAsync(long batchId);
    Task<PageDtoData<long, ProjectsDto>> GetProjectStatisticsQueryAsync(long batchId, PagedQueryCriteria<RptProjectQueryCriteria> criteria);
}
