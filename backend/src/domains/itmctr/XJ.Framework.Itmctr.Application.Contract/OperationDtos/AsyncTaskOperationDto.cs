using XJ.Framework.Itmctr.Domain.Shared.Enums;

namespace XJ.Framework.Itmctr.Application.Contract.OperationDtos;

public class AsyncTaskOperationDto : BaseOperationDto
{
    /// <summary>
    /// 任务编码
    /// </summary>
    public required string TaskCode { get; set; }


    /// <summary>
    /// 业务id
    /// </summary>
    public required string BusinessId { get; set; }

    /// <summary>
    /// 任务数据
    /// </summary>
    public required string TaskData { get; set; }

    /// <summary>
    /// 任务结果
    /// </summary>
    public string? TaskResult { get; set; }

    /// <summary>
    /// 任务数据
    /// </summary>
    public required AsyncTaskStatus TaskStatus { get; set; }

    /// <summary>
    /// 发起人id
    /// </summary>
    public required long ApplyUserId { get; set; }
}