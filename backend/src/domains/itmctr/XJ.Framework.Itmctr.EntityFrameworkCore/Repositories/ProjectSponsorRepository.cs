namespace XJ.Framework.Itmctr.EntityFrameworkCore.Repositories
{
    /// <summary>
    /// 试验主办单位 仓储实现
    /// </summary>
    public class ProjectSponsorRepository : BaseAuditRepository<ItmctrDbContext, long, ProjectSponsorEntity>,
        IProjectSponsorRepository
    {
        public ProjectSponsorRepository(IServiceProvider serviceProvider) : base(
            serviceProvider)
        {
        }

        public async Task<bool> DetachAndInsertAsync(List<ProjectSponsorEntity> entities)
        {
            entities.ForEach(entity =>
            {
                DetachWhenExists(entity.Key);
            });
            return await base.InsertAsync(entities);
        }

        public async Task<bool> DetachAndDeleteAsync(List<ProjectSponsorEntity> entities)
        {
            entities.ForEach(entity =>
            {
                DetachWhenExists(entity.Key);
            });
            return await base.DeleteAsync(entities);
        }
    }
}
