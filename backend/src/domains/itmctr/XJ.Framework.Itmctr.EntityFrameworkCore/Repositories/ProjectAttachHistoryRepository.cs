using XJ.Framework.Itmctr.EntityFrameworkCore;

namespace XJ.Framework.Rbac.EntityFrameworkCore.Repositories;

/// <summary>  
/// AsyncTask 仓储实现  
/// </summary>  
public class ProjectAttachHistoryRepository : BaseAuditRepository<ItmctrDbContext, Guid, ProjectAttachHistoryEntity>,
   IProjectAttachHistoryRepository
{
    public ProjectAttachHistoryRepository(IServiceProvider serviceProvider) : base(
        serviceProvider)
    {
    }
}
