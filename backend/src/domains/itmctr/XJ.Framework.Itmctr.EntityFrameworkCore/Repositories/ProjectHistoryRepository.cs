using System.Linq;
using System.Linq.Expressions;
using XJ.Framework.Itmctr.EntityFrameworkCore;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Common;
using XJ.Framework.Library.Domain.Entities;

namespace XJ.Framework.Rbac.EntityFrameworkCore.Repositories;

/// <summary>  
/// 项目 仓储实现  
/// </summary>  
public class ProjectHistoryRepository : BaseAuditRepository<ItmctrDbContext, long, ProjectHistoryEntity>,
   IProjectHistoryRepository
{
    public ProjectHistoryRepository(IServiceProvider serviceProvider) : base(
        serviceProvider)
    {
        
    }

    public async Task<PageData<long, ProjectHistoryEntity>> GetProjectHistoryPageAsync(
      Expression<Func<ProjectHistoryEntity, bool>> whereLambda, int rowIndex,
      int pageSize,
      List<OrderbyDirection<ProjectHistoryEntity>> orderBy, bool isNoTracking = true)
    {
        whereLambda = whereLambda.And(q => !q.Deleted);
        
        var queryable = await GetQueryableAsync();
        var data = queryable.Where(whereLambda)
            .Orderby<long, ProjectHistoryEntity>(orderBy);

        var pageData = new PageData<long, ProjectHistoryEntity>
        {
            Totals = await data.CountAsync(),
            Rows = await data.Skip(rowIndex).Take(pageSize).ToListAsync()
        };

        return pageData;
    }
}
