<Project Sdk="Microsoft.NET.Sdk">

    <Import Project="..\..\..\..\Common.Secrets.props" />
    <Import Project="..\..\..\..\Common.props" />

    <ItemGroup>
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.EntityFrameworkCore\XJ.Framework.Library.EntityFrameworkCore.csproj" />
        <ProjectReference Include="..\XJ.Framework.Itmctr.Application.Contract\XJ.Framework.Itmctr.Application.Contract.csproj" />
        <ProjectReference Include="..\XJ.Framework.Itmctr.Domain\XJ.Framework.Itmctr.Domain.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="Migrations\" />
    </ItemGroup>

</Project> 