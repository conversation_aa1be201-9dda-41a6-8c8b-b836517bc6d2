namespace XJ.Framework.Rbac.Domain.Entities;

/// <summary>
/// 数据权限缓存实体
/// </summary>
[Table("data_permission_cache", Schema = "r")]
[SoftDeleteIndex("IX_data_permission_cache_expire", nameof(ExpireTime))]
public class DataPermissionCacheEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 用户ID
    /// </summary>
    /// <remarks>
    /// 关联用户表的主键
    /// </remarks>
    [Column("user_id")]
    public required long UserId { get; set; }

    /// <summary>
    /// 角色ID
    /// </summary>
    /// <remarks>
    /// 关联角色表的主键
    /// </remarks>
    [Column("role_id")]
    public required long RoleId { get; set; }

    /// <summary>
    /// 资源类型
    /// </summary>
    /// <remarks>
    /// 需要进行数据权限控制的资源类型名称
    /// </remarks>
    [Column("resource_type")]
    [StringLength(100)]
    public required string ResourceType { get; set; } = null!;

    /// <summary>
    /// 权限范围
    /// </summary>
    /// <remarks>
    /// 用户在该资源类型下的数据访问范围，通常是一个JSON字符串，描述具体的访问规则
    /// </remarks>
    [Column("permission_scope")]
    [StringLength(-1)]
    public string? PermissionScope { get; set; }

    /// <summary>
    /// 缓存时间
    /// </summary>
    /// <remarks>
    /// 数据权限规则被缓存的时间点
    /// </remarks>
    [Column("cache_time")]
    public required DateTimeOffset CacheTime { get; set; }

    /// <summary>
    /// 过期时间
    /// </summary>
    /// <remarks>
    /// 缓存的过期时间点，超过这个时间需要重新计算权限规则
    /// </remarks>
    [Column("expire_time")]
    public required DateTimeOffset ExpireTime { get; set; }
}