namespace XJ.Framework.Rbac.Domain.Entities;

/// <summary>
/// 用户实体
/// </summary>
[Table("users", Schema = "r")]
[SoftDeleteIndex("IX_Users_Username", nameof(Username), IsUnique = true)]
[SoftDeleteIndex("IX_Users_Mobile", nameof(Mobile))]
[SoftDeleteIndex("IX_Users_Email", nameof(Email))]
public class UserEntity : BaseSoftDeleteEntity<long>
{
    /// <summary>
    /// 用户名
    /// </summary>
    /// <remarks>
    /// 用户登录系统的唯一标识，不可重复
    /// </remarks>
    [Column("username")]
    [StringLength(100)]
    public required string Username { get; set; } = null!;

    /// <summary>
    /// 密码哈希
    /// </summary>
    /// <remarks>
    /// 存储用户密码的哈希值，用于安全验证
    /// </remarks>
    [Column("password_hash")]
    [StringLength(200)]
    public required string PasswordHash { get; set; } = null!;

    /// <summary>
    /// 密码盐
    /// </summary>
    /// <remarks>
    /// 用于密码加密的随机盐值
    /// </remarks>
    [Column("password_salt")]
    [StringLength(100)]
    public required string PasswordSalt { get; set; } = null!;

    /// <summary>
    /// 真实姓名
    /// </summary>
    /// <remarks>
    /// 用户的真实姓名，用于显示和识别
    /// </remarks>
    [Column("real_name")]
    [StringLength(100)]
    public required string RealName { get; set; } = null!;

    /// <summary>
    /// 邮箱
    /// </summary>
    /// <remarks>
    /// 用户的电子邮箱地址，可用于登录和通知
    /// </remarks>
    [Column("email")]
    [StringLength(200)]
    public string? Email { get; set; }

    /// <summary>
    /// 邮箱是否已验证
    /// </summary>
    /// <remarks>
    /// 标识用户的邮箱是否已经通过验证
    /// </remarks>
    [Column("email_confirmed")]
    public required bool EmailConfirmed { get; set; }

    /// <summary>
    /// 邮箱验证时间
    /// </summary>
    /// <remarks>
    /// 记录用户邮箱验证通过的时间
    /// </remarks>
    [Column("email_confirmed_time")]
    public DateTimeOffset? EmailConfirmedTime { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    /// <remarks>
    /// 用户的手机号码，可用于登录和通知
    /// </remarks>
    [Column("mobile")]
    [StringLength(40)]
    public string? Mobile { get; set; }

    /// <summary>
    /// 手机号是否已验证
    /// </summary>
    /// <remarks>
    /// 标识用户的手机号是否已经通过验证
    /// </remarks>
    [Column("mobile_confirmed")]
    public required bool MobileConfirmed { get; set; }

    /// <summary>
    /// 手机号验证时间
    /// </summary>
    /// <remarks>
    /// 记录用户手机号验证通过的时间
    /// </remarks>
    [Column("mobile_confirmed_time")]
    public DateTimeOffset? MobileConfirmedTime { get; set; }

    /// <summary>
    /// 头像URL
    /// </summary>
    /// <remarks>
    /// 用户头像的访问地址
    /// </remarks>
    [Column("avatar")]
    [StringLength(400)]
    public string? Avatar { get; set; }

    /// <summary>
    /// 用户类型
    /// </summary>
    /// <remarks>
    /// Internal: 内部用户
    /// External: 外部用户
    /// System: 系统用户
    /// </remarks>
    [Column("user_type")]
    public required UserType UserType { get; set; }

    /// <summary>
    /// 用户状态
    /// </summary>
    /// <remarks>
    /// Disabled: 禁用
    /// Enabled: 启用
    /// Locked: 锁定
    /// </remarks>
    [Column("status")]
    public required UserStatus Status { get; set; }

    /// <summary>
    /// 最后登录时间
    /// </summary>
    /// <remarks>
    /// 记录用户最近一次成功登录的时间
    /// </remarks>
    [Column("last_login_time")]
    public DateTimeOffset? LastLoginTime { get; set; }

    /// <summary>
    /// 最后登录IP
    /// </summary>
    /// <remarks>
    /// 记录用户最近一次登录的IP地址
    /// </remarks>
    [Column("last_login_ip")]
    [StringLength(100)]
    public string? LastLoginIp { get; set; }

    /// <summary>
    /// 密码错误次数
    /// </summary>
    /// <remarks>
    /// 记录用户连续输入错误密码的次数，用于账户锁定判断
    /// </remarks>
    [Column("password_error_count")]
    public required int PasswordErrorCount { get; set; }

    /// <summary>
    /// 密码更新时间
    /// </summary>
    /// <remarks>
    /// 记录用户最近一次修改密码的时间，用于密码过期判断
    /// </remarks>
    [Column("password_update_time")]
    public DateTimeOffset? PasswordUpdateTime { get; set; }
}