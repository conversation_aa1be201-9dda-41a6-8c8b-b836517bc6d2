<Project Sdk="Microsoft.NET.Sdk">

    <Import Project="..\..\..\..\Common.Secrets.props" />
    <Import Project="..\..\..\..\Common.props" />

    <ItemGroup>
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.Domain\XJ.Framework.Library.Domain.csproj" />
        <ProjectReference Include="..\XJ.Framework.Rbac.Domain.Shared\XJ.Framework.Rbac.Domain.Shared.csproj" />
    </ItemGroup>
    
    <ItemGroup>
      <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" />
      <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
    </ItemGroup>

</Project> 