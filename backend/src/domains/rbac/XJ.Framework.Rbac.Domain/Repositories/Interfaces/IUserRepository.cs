namespace XJ.Framework.Rbac.Domain.Repositories.Interfaces;

/// <summary>
/// 用户仓储接口
/// </summary>
public interface IUserRepository : IAuditRepository<long, UserEntity>
{
    /// <summary>
    /// 根据用户名查找用户
    /// </summary>
    Task<UserEntity?> FindByUsernameAsync(string username);

    /// <summary>
    /// 根据用户名查找用户
    /// </summary>
    Task<UserEntity?> FindByUsernameAsync(string username, List<UserType> userTypes);

    /// <summary>
    /// 根据邮箱查找用户
    /// </summary>
    Task<UserEntity?> FindByEmailAsync(string? email);

    /// <summary>
    /// 根据手机号查找用户
    /// </summary>
    Task<UserEntity?> FindByPhoneNumberAsync(string? phoneNumber);

    /// <summary>
    /// 检查用户名是否存在
    /// </summary>
    Task<bool> IsUsernameExistsAsync(string username);

    /// <summary>
    /// 检查邮箱是否存在
    /// </summary>
    Task<bool> IsEmailExistsAsync(string? email);

    /// <summary>
    /// 检查手机号是否存在
    /// </summary>
    Task<bool> IsPhoneNumberExistsAsync(string? phoneNumber);

    /// <summary>
    /// 更新用户密码
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="passwordHash">密码哈希</param>
    /// <param name="salt">密码盐</param>
    Task<bool> UpdatePasswordAsync(long userId, string passwordHash, string salt);

    /// <summary>
    /// 更新用户状态
    /// </summary>
    Task<bool> UpdateStatusAsync(long userId, UserStatus status);

    /// <summary>
    /// 更新用户最后登录信息
    /// </summary>
    Task<bool> UpdateLastLoginInfoAsync(long userId, string? ip, string? deviceInfo);

    /// <summary>
    /// 更新用户邮箱验证状态
    /// </summary>
    Task<bool> UpdateEmailVerificationStatusAsync(long userId, bool isVerified);

    /// <summary>
    /// 更新用户手机验证状态
    /// </summary>
    Task<bool> UpdatePhoneVerificationStatusAsync(long userId, bool isVerified);

    /// <summary>
    /// 增加密码错误次数
    /// </summary>
    Task<int> IncrementPasswordErrorCountAsync(long userId);

    /// <summary>
    /// 重置密码错误次数
    /// </summary>
    Task<bool> ResetPasswordErrorCountAsync(long userId);
}