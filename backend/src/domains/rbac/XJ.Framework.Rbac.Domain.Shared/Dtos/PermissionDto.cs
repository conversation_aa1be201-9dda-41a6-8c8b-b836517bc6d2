using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Rbac.Domain.Shared.Enums;
using HttpMethod = XJ.Framework.Rbac.Domain.Shared.Enums.HttpMethod;

namespace XJ.Framework.Rbac.Domain.Shared.Dtos;

/// <summary>
/// Permission DTO
/// </summary>
public class PermissionDto : BaseDto<long>
{
    /// <summary>
    /// 应用ID
    /// </summary>
    /// <remarks>
    /// 标识权限所属的应用系统
    /// 为null时表示是公共权限
    /// </remarks>
    public string? AppId { get; set; }

    /// <summary>
    /// 应用编码
    /// </summary>
    /// <remarks>
    /// 应用系统的唯一标识符
    /// 为null时表示是公共权限
    /// </remarks>
    public string? AppCode { get; set; }

    /// <summary>
    /// 父级权限ID
    /// </summary>
    /// <remarks>
    /// 用于构建权限的层级结构，顶级权限的父级ID为null
    /// </remarks>
    public long? ParentId { get; set; }

    /// <summary>
    /// 权限编码
    /// </summary>
    /// <remarks>
    /// 权限的唯一标识符，用于权限验证
    /// 建议格式：{app_code}:{module}:{action}
    /// 例如：oa:document:create
    /// </remarks>
    public string Code { get; set; } = null!;

    /// <summary>
    /// 权限名称
    /// </summary>
    /// <remarks>
    /// 权限的显示名称，用于界面展示
    /// </remarks>
    public string Name { get; set; } = null!;

    /// <summary>
    /// 权限类型
    /// </summary>
    /// <remarks>
    /// 1: 目录
    /// 2: 菜单
    /// 3: 按钮
    /// 4: API接口
    /// </remarks>
    public PermissionType Type { get; set; }

    /// <summary>
    /// 路由路径
    /// </summary>
    /// <remarks>
    /// 前端路由的访问路径，用于菜单导航
    /// API类型权限时表示API路径
    /// </remarks>
    public string? Path { get; set; }

    /// <summary>
    /// 前端组件
    /// </summary>
    /// <remarks>
    /// 前端组件的路径，用于动态加载组件
    /// </remarks>
    public string? Component { get; set; }

    /// <summary>
    /// 重定向地址
    /// </summary>
    /// <remarks>
    /// 当访问该路由时需要重定向到的地址
    /// </remarks>
    public string? Redirect { get; set; }

    /// <summary>
    /// 图标
    /// </summary>
    /// <remarks>
    /// 菜单或按钮的图标标识
    /// </remarks>
    public string? Icon { get; set; }

    /// <summary>
    /// HTTP方法
    /// </summary>
    /// <remarks>
    /// 支持的HTTP请求方法：GET, POST, PUT, DELETE等
    /// 主要用于API接口权限控制
    /// </remarks>
    public HttpMethod? Method { get; set; }

    /// <summary>
    /// 排序号
    /// </summary>
    /// <remarks>
    /// 用于控制同级权限的显示顺序，数值越小越靠前
    /// </remarks>
    public int SortOrder { get; set; }

    /// <summary>
    /// 状态
    /// </summary>
    /// <remarks>
    /// 1: 启用
    /// 0: 禁用
    /// </remarks>
    public CommonStatus Status { get; set; }

    /// <summary>
    /// 权限描述
    /// </summary>
    /// <remarks>
    /// 对权限功能的详细描述说明
    /// </remarks>
    public string? Description { get; set; }
}