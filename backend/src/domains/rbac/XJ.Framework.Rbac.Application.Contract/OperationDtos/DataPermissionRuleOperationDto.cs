namespace XJ.Framework.Rbac.Application.Contract.OperationDtos;

/// <summary>
/// DataPermissionRule 操作 DTO
/// </summary>
public class DataPermissionRuleOperationDto : BaseOperationDto
{
    /// <summary>
    /// 角色ID（关联角色表的主键）
    /// </summary>
    public long RoleId { get; set; }

    /// <summary>
    /// 实体类型名称（需要进行数据权限控制的实体类型名称）
    /// </summary>
    public string EntityTypeName { get; set; } = null!;

    /// <summary>
    /// 规则类型：0-全部数据，1-本人数据，2-部门数据，3-自定义规则
    /// </summary>
    public int RuleType { get; set; }

    /// <summary>
    /// 规则值（根据规则类型存储对应的规则值）
    /// </summary>
    public string? RuleValue { get; set; }

    /// <summary>
    /// 状态：true-启用，false-禁用
    /// </summary>
    public bool Status { get; set; }

    /// <summary>
    /// 备注说明（用于记录规则的补充说明信息）
    /// </summary>
    public string? Remark { get; set; }

} 