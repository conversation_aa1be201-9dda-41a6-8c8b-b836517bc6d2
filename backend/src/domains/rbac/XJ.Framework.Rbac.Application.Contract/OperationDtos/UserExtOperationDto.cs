using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel.DataAnnotations;
using XJ.Framework.Library.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.Application.Contract.OperationDtos;

/// <summary>
/// User 操作 DTO
/// </summary>
public class UserExtOperationDto : BaseOperationDto
{
    /// <summary>
    /// 用户ID
    /// </summary>
    public required long UserId { get; set; }

    /// <summary>
    /// 性别
    /// </summary>
    public string? Gender { get; set; }

    /// <summary>
    /// 国家
    /// </summary>
    public string? Country { get; set; }

    /// <summary>
    /// 注册单位名称
    /// </summary>
    public string? Unit { get; set; }

    /// <summary>
    /// 联系地址
    /// </summary>
    public string? ContactAddress { get; set; }

    /// <summary>
    /// 固定电话
    /// </summary>
    public string? Telephone { get; set; }
}