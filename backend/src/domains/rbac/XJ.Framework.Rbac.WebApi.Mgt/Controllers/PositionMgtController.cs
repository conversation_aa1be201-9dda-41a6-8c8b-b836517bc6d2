
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Rbac.WebApi.Mgt.Controllers;

/// <summary>
/// 岗位相关
/// </summary>

public class PositionController : BaseEditableAppController<long, PositionDto, PositionOperationDto, IPositionService, PositionQueryCriteria>
{
    public PositionController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    /// <summary>
    /// 插入岗位
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(PositionOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    /// <summary>
    /// 根据ID获取岗位
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id:long}")]
    public async Task<PositionDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    /// <summary>
    /// 根据ID更新岗位
    /// </summary>
    /// <param name="id"></param>
    /// <param name="dto"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, PositionOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    /// <summary>
    /// 根据ID删除岗位
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    /// <summary>
    /// 分页查询岗位列表
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet("page")]
    public async Task<PageDtoData<long, PositionDto>> GetPageAsync([FromQuery] PagedQueryCriteria<PositionQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    /// <summary>
    /// 查询岗位列表
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IEnumerable<PositionDto>> GetListAsync([FromQuery] PositionQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
