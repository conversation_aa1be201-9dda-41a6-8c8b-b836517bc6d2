
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Rbac.WebApi.Mgt.Controllers;

/// <summary>
/// DataPermissionRule 控制器
/// </summary>

public class DataPermissionRuleController : BaseEditableAppController<long, DataPermissionRuleDto, DataPermissionRuleOperationDto, IDataPermissionRuleService, DataPermissionRuleQueryCriteria>
{
    public DataPermissionRuleController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    /*
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(DataPermissionRuleOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    [HttpGet("{id:long}")]
    public async Task<DataPermissionRuleDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, DataPermissionRuleOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, DataPermissionRuleDto>> GetPageAsync([FromQuery] PagedQueryCriteria<DataPermissionRuleQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<DataPermissionRuleDto>> GetListAsync([FromQuery] DataPermissionRuleQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
    */
}
