using XJ.Framework.Library.Domain.Attributes;
using XJ.Framework.Library.WebApi.Attributes;

namespace XJ.Framework.Rbac.WebApi.Mgt.Controllers;

/// <summary>
/// 用户组织相关
/// </summary>
public class UserOrganizationController : BaseEditableAppController<long, UserOrganizationDto,
    UserOrganizationOperationDto, IUserOrganizationService, UserOrganizationQueryCriteria>
{
    public UserOrganizationController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }


    /// <summary>
    /// 更新用户组织
    /// </summary>
    /// <param name="userId">用户id</param>
    /// <param name="userOrganizations"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPut("{userId:long}")]
    public async Task<bool> UpdateAsync(long userId, [FromBody] List<UserOrganizationOperationDto> userOrganizations)
    {
        return await Service.SetUserOrganizations(userId, userOrganizations);
    }

    /// <summary>
    /// 查询用户组织
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet]
    [PublicPermission(true)]
    public async Task<IEnumerable<UserOrganizationDto>> GetListAsync([FromQuery] UserOrganizationQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
