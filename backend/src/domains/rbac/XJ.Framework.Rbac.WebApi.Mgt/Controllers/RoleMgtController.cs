using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Rbac.WebApi.Mgt.Controllers;

/// <summary>
/// 角色相关
/// </summary>
public class
    RoleController : BaseEditableAppController<long, RoleDto, RoleOperationDto, IRoleService, RoleQueryCriteria>
{
    public RoleController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }

    /// <summary>
    /// 插入角色
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(RoleOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    /// <summary>
    /// 根据ID获取角色
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [HttpGet("{id:long}")]
    public async Task<RoleDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    /// <summary>
    /// 根据ID更新角色
    /// </summary>
    /// <param name="id"></param>
    /// <param name="dto"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, RoleOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    /// <summary>
    /// 根据ID删除角色
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    /// <summary>
    /// 分页查询角色列表
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet("page")]
    public async Task<PageDtoData<long, RoleDto>> GetPageAsync(
        [FromQuery] PagedQueryCriteria<RoleQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    /// <summary>
    /// 查询角色列表
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet]
    public async Task<IEnumerable<RoleDto>> GetListAsync([FromQuery] RoleQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
