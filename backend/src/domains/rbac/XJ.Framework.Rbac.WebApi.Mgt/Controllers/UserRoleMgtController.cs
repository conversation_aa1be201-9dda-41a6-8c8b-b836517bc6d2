using XJ.Framework.Library.Domain.Attributes;
using XJ.Framework.Library.WebApi.Attributes;

namespace XJ.Framework.Rbac.WebApi.Mgt.Controllers;

/// <summary>
/// 用户角色相关
/// </summary>
public class UserRoleController : BaseEditableAppController<long, UserRoleDto, UserRoleOperationDto, IUserRoleService,
    UserRoleQueryCriteria>
{
    public UserRoleController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }


    /// <summary>
    /// 更新用户角色
    /// </summary>
    /// <param name="userId"></param>
    /// <param name="userRoles"></param>
    /// <returns></returns>
    [UnitOfWork]
    [HttpPut("{userId:long}")]
    public async Task<bool> UpdateAsync(long userId, [FromBody] List<UserRoleOperationDto> userRoles)
    {
        return await Service.SetUserRoles(userId, userRoles);
    }


    /// <summary>
    /// 查询用户角色
    /// </summary>
    /// <param name="criteria"></param>
    /// <returns></returns>
    [HttpGet]
    [PublicPermission(true)]
    public async Task<IEnumerable<UserRoleDto>> GetListAsync([FromQuery] UserRoleQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
}
