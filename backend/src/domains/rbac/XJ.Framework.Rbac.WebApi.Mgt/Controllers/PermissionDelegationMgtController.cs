
using XJ.Framework.Library.Domain.Attributes;

namespace XJ.Framework.Rbac.WebApi.Mgt.Controllers;

/// <summary>
/// PermissionDelegation 控制器
/// </summary>

public class PermissionDelegationController : BaseEditableAppController<long, PermissionDelegationDto, PermissionDelegationOperationDto, IPermissionDelegationService, PermissionDelegationQueryCriteria>
{
    public PermissionDelegationController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
    
    /*
    [UnitOfWork]
    [HttpPost]
    public async Task<bool> InsertAsync(PermissionDelegationOperationDto dto)
    {
        return await Service.CreateAsync(dto);
    }

    [HttpGet("{id:long}")]
    public async Task<PermissionDelegationDto?> GetAsync(long id)
    {
        return await Service.GetByIdAsync(id);
    }

    [UnitOfWork]
    [HttpPut("{id:long}")]
    public async Task<bool> UpdateAsync(long id, PermissionDelegationOperationDto dto)
    {
        return await Service.UpdateAsync(id, dto);
    }

    [UnitOfWork]
    [HttpDelete]
    public async Task<bool> DeleteAsync(long id)
    {
        return await Service.DeleteAsync(id);
    }

    [HttpGet("page")]
    public async Task<PageDtoData<long, PermissionDelegationDto>> GetPageAsync([FromQuery] PagedQueryCriteria<PermissionDelegationQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }

    [HttpGet]
    public async Task<IEnumerable<PermissionDelegationDto>> GetListAsync([FromQuery] PermissionDelegationQueryCriteria criteria)
    {
        return await Service.GetListAsync(criteria);
    }
    */
}
