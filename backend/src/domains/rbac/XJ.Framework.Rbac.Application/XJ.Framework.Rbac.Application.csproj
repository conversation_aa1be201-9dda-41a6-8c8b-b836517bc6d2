<Project Sdk="Microsoft.NET.Sdk">

    <Import Project="..\..\..\..\Common.Secrets.props" />
    <Import Project="..\..\..\..\Common.props" />

    <ItemGroup>
        <ProjectReference Include="..\..\..\core\impls\XJ.Framework.Library.Image\XJ.Framework.Library.Image.csproj" />
        <ProjectReference Include="..\..\..\shared\XJ.Framework.Library.Application\XJ.Framework.Library.Application.csproj" />
        <ProjectReference Include="..\..\messaging\XJ.Framework.Messaging.ApiClient\XJ.Framework.Messaging.ApiClient.csproj" />
        <ProjectReference Include="..\XJ.Framework.Rbac.Application.Contract\XJ.Framework.Rbac.Application.Contract.csproj" />
        <ProjectReference Include="..\XJ.Framework.Rbac.EntityFrameworkCore\XJ.Framework.Rbac.EntityFrameworkCore.csproj" />
    </ItemGroup>

</Project> 