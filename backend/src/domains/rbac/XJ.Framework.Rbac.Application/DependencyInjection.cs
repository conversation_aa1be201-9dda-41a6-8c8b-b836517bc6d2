using Microsoft.Extensions.Caching.Memory;
using System;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using XJ.Framework.Library.Cache.Abstraction;
using XJ.Framework.Rbac.Application.Contract.Interfaces;
using XJ.Framework.Rbac.Application.Services;

namespace XJ.Framework.Rbac.Application;

/// <summary>
/// RBAC应用层依赖注入扩展
/// </summary>
public static class DependencyInjection
{
    /// <summary>
    /// 添加RBAC应用层服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddRbacApplication(this IServiceCollection services, IConfiguration configuration)
    {
        return services;
    }

  
}