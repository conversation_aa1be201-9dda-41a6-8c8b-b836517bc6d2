using XJ.Framework.Library.Domain.Id;

namespace XJ.Framework.Rbac.Application.Services;

/// <summary>
/// RolePermission 服务实现
/// </summary>
public sealed class RolePermissionService :
    BaseEditableAppService<long, RolePermissionEntity, RolePermissionDto, RolePermissionOperationDto,
        IRolePermissionRepository, RolePermissionQueryCriteria>,
    IRolePermissionService
{
    public RolePermissionService(IRolePermissionRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository,
        mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }

    public async Task<bool> SetPermissions(long roleId, List<RolePermissionOperationDto> rolePermissions)
    {
        var originals = await Repository.GetListAsync(q => q.RoleId == roleId);
        // 使用permissionid 比较原始数据和新数据 需要比较出新增数据、删除数据
        var originalsDict = originals.ToDictionary(x => (x.PermissionId));
        var newDict = rolePermissions.ToDictionary(x => (x.PermissionId));
        var toAdd = new List<RolePermissionEntity>();
        var toDelete = new List<RolePermissionEntity>();

        foreach (var item in originals)
        {
            if (!newDict.ContainsKey(item.PermissionId))
            {
                toDelete.Add(item);
            }
        }

        foreach (var item in rolePermissions)
        {
            if (!originalsDict.ContainsKey(item.PermissionId))
            {
                toAdd.Add(new RolePermissionEntity
                {
                    Key = KeyGenerator.GenerateKey(),
                    RoleId = roleId,
                    PermissionId = item.PermissionId
                });
            }
        }

        // 删除
        if (toDelete.Count > 0)
        {
            await Repository.DeleteAsync(toDelete);
        }

        // 添加
        if (toAdd.Count > 0)
        {
            await Repository.InsertAsync(toAdd);
        }

        return true;
    }
}