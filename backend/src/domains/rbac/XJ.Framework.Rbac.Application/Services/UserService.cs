using System;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.Extensions.Logging;
using Serilog;
using System.Security.Authentication;
using XJ.Framework.Library.Common.Abstraction;
using XJ.Framework.Library.Common.Abstraction.Extensions;
using XJ.Framework.Library.Domain.Id;
using XJ.Framework.Library.Domain.Shared.Dtos;
using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Library.Domain.UOW;
using XJ.Framework.Rbac.Application.Contract.Interfaces;
using XJ.Framework.Rbac.Domain.Entities;
using XJ.Framework.Rbac.Domain.Repositories.Interfaces;
using XJ.Framework.Rbac.Domain.Shared.Dtos;
using XJ.Framework.Rbac.Application.Contract.OperationDtos;
using XJ.Framework.Rbac.Domain;
using XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;
using XJ.Framework.Rbac.Domain.Shared.Enums;
using System.Security.Cryptography;
using System.Text;
using XJ.Framework.Library.Application.Contract.Interfaces;

namespace XJ.Framework.Rbac.Application.Services;

/// <summary>
/// User 服务实现
/// </summary>
public sealed class UserService :
    BaseEditableAppService<long, UserEntity, UserDto, UserOperationDto, IUserRepository, UserQueryCriteria>,
    IUserService
{
    private readonly ICaptchaService _captchaService;
    private readonly IUserRoleService _userRoleService;
    private readonly IUserPositionService _userPositionService;
    private readonly IUserOrganizationRepository _userOrganizationRepository;
    private readonly IOrganizationRepository _organizationRepository;
    private readonly IUserPositionRepository _userPositionRepository;
    private readonly IPositionRepository _positionRepository;
    private readonly IUserExtService _userExtService;
    private readonly IRoleService _roleService;
    private readonly IUserExtRepository _userExtRepository;

    private const string DefaultRoleCode = "RESEARCHER";

    public UserService(IUserRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ILogger<UserService> logger, ICurrentUserContext currentUserContext,
        IUserRoleService userRoleService, IUserPositionService userPositionService,
        IUserOrganizationRepository userOrganizationRepository, IOrganizationRepository organizationRepository,
        IUserPositionRepository userPositionRepository, IPositionRepository positionRepository,
        IUserExtService userExtService, IRoleService roleService, IUserExtRepository userExtRepository,
        ICaptchaService captchaService) : base(
        repository,
        mapper, unitOfWork, keyGenerator, currentUserContext)
    {
        _logger = logger;
        _userRoleService = userRoleService;
        _userPositionService = userPositionService;
        _userOrganizationRepository = userOrganizationRepository;
        _organizationRepository = organizationRepository;
        _userPositionRepository = userPositionRepository;
        _positionRepository = positionRepository;
        _userExtService = userExtService;
        _roleService = roleService;
        _userExtRepository = userExtRepository;
        _captchaService = captchaService;
    }

    private readonly ILogger<UserService> _logger;

    public async new Task<bool> CreateAsync(UserOperationDto dto)
    {
        await CreateUserAsync(dto);
        return true;
    }

    public async new Task<bool> DeleteAsync(long id)
    {
        var entity = await Repository.GetAsync(id);
        if (entity!.UserType == UserType.System)
        {
            throw new ValidationException("系统用户不允许删除");
        }

        return await base.DeleteAsync(id);
    }

    public async new Task<IEnumerable<UserDto>> GetListAsync(UserQueryCriteria criteria)
    {
        //如果当前用户不是系统用户 则强制不查询系统用户
        if (CurrentUserContext.GetCurrentUser()!.UserType != UserType.System)
        {
            criteria.NotUserType = UserType.System;
        }

        return await base.GetListAsync(criteria);
    }

    public async Task<long> CreateUserAsync(UserCreateOperationDto dto)
    {
        if (!await IsUsernameAvailableAsync(dto.Username))
        {
            throw new ValidationException("用户名已存在/ Username already exists");
        }

        var salt = dto.PasswordSalt;
        var passwordHash = dto.PasswordHash;

        var user = new UserEntity
        {
            Username = dto.Username,
            PasswordHash = passwordHash,
            PasswordSalt = salt,
            Email = dto.Email,
            Mobile = dto.Mobile,
            Status = dto.Status,

            RealName = dto.RealName,
            EmailConfirmed = false,
            MobileConfirmed = false,
            UserType = dto.UserType,
            PasswordErrorCount = 0,
            Key = IdGenerator.NextId()
        };

        await Repository.InsertAsync(user);

        var userExt = new UserExtEntity()
        {
            Key = IdGenerator.NextId(),
            UserId = user.Key,
            Gender = dto.Gender,
            Country = dto.Country,
            Unit = dto.Unit,
            ContactAddress = dto.ContactAddress,
            Telephone = dto.Telephone,
        };

        await _userExtRepository.InsertAsync(userExt);

        return user.Key;
    }


    /// <summary>
    /// 检查用户名是否可用
    /// </summary>
    public async Task<bool> IsUsernameAvailableAsync(string username)
    {
        return !await Repository.IsUsernameExistsAsync(username);
    }

    /// <summary>
    /// 检查邮箱是否可用
    /// </summary>
    public async Task<bool> IsEmailAvailableAsync(string email)
    {
        return !await Repository.IsEmailExistsAsync(email);
    }

    /// <summary>
    /// 检查手机号是否可用
    /// </summary>
    public async Task<bool> IsPhoneNumberAvailableAsync(string phoneNumber)
    {
        return !await Repository.IsPhoneNumberExistsAsync(phoneNumber);
    }

    public async Task<List<UserDto>> GetUsersByRoleCodeAsync(string roleCode, params RoleType[] roleTypes)
    {
        var userIds = await _userRoleService.GetRoleUsersAsync(roleCode, roleTypes);
        var userEntities =
            (await Repository.GetListAsync(q => userIds.Any(u => u.Equals(q.Key)))).ToList();
        return Mapper.Map<List<UserDto>>(userEntities);
    }

    public async Task<List<UserDto>> GetUsersByPositionCodeAsync(string positionCode, string organizationCode)
    {
        var userIds = await _userPositionService.GetPositionUsersAsync(positionCode, organizationCode);
        var userEntities =
            (await Repository.GetListAsync(q => userIds.Any(u => u.Equals(q.Key)))).ToList();
        return Mapper.Map<List<UserDto>>(userEntities);
    }


    public async Task<List<UserDto>> GetUsersByPositionCodeAsync(string positionCode)
    {
        var userIds = await _userPositionService.GetPositionUsersAsync(positionCode);
        var userEntities =
            (await Repository.GetListAsync(q => userIds.Any(u => u.Equals(q.Key)))).ToList();
        return Mapper.Map<List<UserDto>>(userEntities);
    }

    public async Task<List<OrganizationUserDto>> GetManagedPositionUsersAsync(string positionCode,
        long userId)
    {
        var position = await _positionRepository.GetAsync(q => q.Code.ToLower().Equals(positionCode.ToLower()));
        position.NullCheck();

        var userOrganizationEntities =
            (await _userOrganizationRepository.GetListAsync(q => q.UserId == userId)).ToList();

        var orgIds = userOrganizationEntities.Select(u => u.OrganizationId).ToList();

        var organizations =
            (await _organizationRepository.GetListAsync(q => orgIds.Contains(q.Key))).ToList();
        var organizationDtos = Mapper.Map<List<OrganizationDto>>(organizations);

        var userPositionEntities =
            (await _userPositionRepository.GetListAsync(q =>
                q.Status == UserPositionStatus.Active
                &&
                (
                    (!q.StartTime.HasValue && !q.EndTime.HasValue)
                    ||
                    (q.StartTime.HasValue && q.StartTime.Value <= DateTime.Now && q.EndTime.HasValue &&
                     q.EndTime >= DateTime.Now)
                )
                &&
                q.PositionId == position!.Key
            )).ToList();

        var organizationCodes = organizations.Select(o => o.Code.ToLower()).ToList();

        var children =
            await _organizationRepository.GetOrganizationChildrenAsync(organizationCodes,
                true);

        var result = organizationDtos.Select(o =>
        {
            var orgChildren = children.ContainsKey(o.Code.ToLower())
                ? children[o.Code.ToLower()]
                : new List<OrganizationEntity>();

            var positionUsers = userPositionEntities
                .Where(u => orgChildren.Any(c => c.Key == u.OrganizationId)).ToList();
            return new OrganizationUserDto()
            {
                Organization = Mapper.Map<OrganizationDto>(o),
                Users = positionUsers.Select(u => new UserDto()
                {
                    Key = u.UserId
                }).ToList()
            };
        }).ToList();

        var userIds = result.SelectMany(r => r.Users).Select(u => u.Key).ToList();

        var users = (await Repository.GetListAsync(q =>
            userIds.Contains(q.Key))).ToList();

        result.ForEach(r =>
        {
            var userList = r.Users.Select(u => users.FirstOrDefault(x => x.Key == u.Key)).ToList();
            r.Users = Mapper.Map<List<UserDto>>(userList);
        });

        return result;
    }


    public async Task<long> CreateUserAsync(UserOperationDto dto)
    {
        try
        {
            // 1. 验证用户名是否可用
            if (!await IsUsernameAvailableAsync(dto.Username))
            {
                throw new ValidationException("用户名已存在/ Username already exists");
            }

            // 2. 验证邮箱是否可用
            if (!string.IsNullOrEmpty(dto.Email) && !await IsEmailAvailableAsync(dto.Email))
            {
                throw new ValidationException("邮箱已被使用/ Email already in use");
            }

            // 3. 验证手机号是否可用
            if (!string.IsNullOrEmpty(dto.Mobile) && !await IsPhoneNumberAvailableAsync(dto.Mobile))
            {
                throw new ValidationException("手机号已被使用/ Phone number already in use");
            }

            // 4. 验证密码是否符合要求
            dto.Password = PasswordHelper.DecryptAES(dto.Password);
            dto.ConfirmPassword = PasswordHelper.DecryptAES(dto.ConfirmPassword);
            if (!dto.Password.Equals(dto.ConfirmPassword))
            {
                throw new ValidationException("两次输入的密码不一致/ Passwords do not match");
            }

            //如果未传入salt 说明不是强制创建的用户 需要校验密码强度
            if (!PasswordHelper.IsValidPassword(dto.Password))
            {
                throw new ValidationException(
                    "密码必须为6-20位，且包含大写字母、小写字母、数字和特殊字符/ Password must be 6-20 characters long and contain uppercase letters, lowercase letters, numbers, and special characters");
            }
            // 5. 创建用户实体

            var salt = PasswordHelper.GenerateSalt();
            var passwordHash = PasswordHelper.HashPassword(dto.Password, salt);

            var user = new UserEntity
            {
                Username = dto.Username,
                PasswordHash = passwordHash,
                PasswordSalt = salt,
                Email = dto.Email,
                Mobile = dto.Mobile,
                Status = dto.Status,

                RealName = dto.RealName,
                EmailConfirmed = false,
                MobileConfirmed = false,
                UserType = dto.UserType,
                PasswordErrorCount = 0,
                Key = IdGenerator.NextId()
            };

            // 5. 保存用户
            await Repository.InsertAsync(user);

            return user.Key;

            // // 6. 返回注册结果
            // return new RegisterResultDto
            // {
            //     UserId = user.Key.ToString(),
            //     Username = user.Username
            // };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户添加失败: {Username}", dto.Username);
            throw;
        }
    }

    public async Task<bool> Register(RegisterOperationDto dto)
    {
        try
        {
            //验证验证码输入是否正确
            if (!await _captchaService.ValidateCaptchaAsync(dto.CaptchaId, dto.CaptchaCode))
            {
                throw new AuthenticationException("验证码不正确/Captcha code is incorrect");
            }

            // 1. 验证用户名是否可用
            if (!await IsUsernameAvailableAsync(dto.Username))
            {
                throw new ValidationException("用户名已存在/ Username already exists");
            }

            // 2. 验证邮箱是否可用
            if (!string.IsNullOrEmpty(dto.Email) && !await IsEmailAvailableAsync(dto.Email))
            {
                throw new ValidationException("邮箱已被使用/ Email already in use");
            }

            // 3. 验证手机号是否可用
            if (!string.IsNullOrEmpty(dto.Mobile) && !await IsPhoneNumberAvailableAsync(dto.Mobile))
            {
                throw new ValidationException("手机号已被使用/ Phone number already in use");
            }

            // 4. 验证密码是否符合要求
            dto.Password = PasswordHelper.DecryptAES(dto.Password);
            dto.RePassword = PasswordHelper.DecryptAES(dto.RePassword);
            if (!dto.Password.Equals(dto.RePassword))
            {
                throw new ValidationException("两次输入的密码不一致/ Passwords do not match");
            }

            if (!PasswordHelper.IsValidPassword(dto.Password))
            {
                throw new ValidationException(
                    "密码必须为6-20位，且包含大写字母、小写字母、数字和特殊字符/ Password must be 6-20 characters long and contain uppercase letters, lowercase letters, numbers, and special characters");
            }
            // 5. 创建用户实体

            var salt = PasswordHelper.GenerateSalt();
            var passwordHash = PasswordHelper.HashPassword(dto.Password, salt);
            var user = new UserEntity
            {
                Username = dto.Username,
                PasswordHash = passwordHash,
                PasswordSalt = salt,
                Email = dto.Email,
                Mobile = dto.Mobile,
                Status = UserStatus.Enabled,

                RealName = dto.RealName,
                EmailConfirmed = false,
                MobileConfirmed = false,
                UserType = UserType.External,
                PasswordErrorCount = 0,
                Key = IdGenerator.NextId()
            };
            // 6. 保存用户
            await Repository.InsertAsync(user);
            await _userExtService.RegisterUserExt(dto, user.Key);

            var defaultRole = await _roleService.GetByCodeAsync(DefaultRoleCode);

            await _userRoleService.SetUserRoles(user.Key, [
                new()
                {
                    UserId = user.Key,
                    RoleId = defaultRole!.Key
                }
            ]);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "用户添加失败: {Username}", dto.Username);
            throw;
        }
    }

    public async Task<string> ResetPasswordAsync(long id)
    {
        var user = await Repository.GetAsync(id);
        if (user == null)
        {
            throw new ValidationException("用户不存在/ User does not exist");
        }

        var newPassword = PasswordHelper.GenerateRandomPassword();
        var salt = PasswordHelper.GenerateSalt();
        var passwordHash = PasswordHelper.HashPassword(newPassword, salt);

        user.PasswordHash = passwordHash;
        user.PasswordSalt = salt;
        user.PasswordErrorCount = 0;
        user.PasswordUpdateTime = DateTimeOffset.UtcNow;

        await Repository.UpdateAsync(user);

        return newPassword;
    }
}
