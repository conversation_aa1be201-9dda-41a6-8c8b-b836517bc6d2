
using XJ.Framework.Library.Domain.Id;

namespace XJ.Framework.Rbac.Application.Services;

/// <summary>
/// DataPermissionRule 服务实现
/// </summary>
public sealed class DataPermissionRuleService :
    BaseEditableAppService<long, DataPermissionRuleEntity, DataPermissionRuleDto, DataPermissionRuleOperationDto, IDataPermissionRuleRepository, DataPermissionRuleQueryCriteria>,
    IDataPermissionRuleService
{
    public DataPermissionRuleService(IDataPermissionRuleRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository,
        mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }
} 