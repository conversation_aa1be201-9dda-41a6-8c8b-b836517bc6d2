using XJ.Framework.Library.Domain.Id;

namespace XJ.Framework.Rbac.Application.Services;

/// <summary>
/// DataPermissionCache 服务实现
/// </summary>
public sealed class DataPermissionCacheService :
    BaseEditableAppService<long, DataPermissionCacheEntity, DataPermissionCacheDto, DataPermissionCacheOperationDto,
        IDataPermissionCacheRepository, DataPermissionCacheQueryCriteria>,
    IDataPermissionCacheService
{
    public DataPermissionCacheService(IDataPermissionCacheRepository repository, IMapper mapper, IUnitOfWork unitOfWork,
        IKeyGenerator<long> keyGenerator, ICurrentUserContext currentUserContext) : base(repository,
        mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }
}