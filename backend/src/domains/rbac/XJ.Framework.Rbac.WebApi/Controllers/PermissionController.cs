using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Library.WebApi.Attributes;
using XJ.Framework.Rbac.Application.Contract;
using XJ.Framework.Rbac.Domain.Shared.Dtos.Auth;
using XJ.Framework.Rbac.Domain.Shared.Enums;
using HttpMethod = XJ.Framework.Rbac.Domain.Shared.Enums.HttpMethod;

namespace XJ.Framework.Rbac.WebApi.Controllers;

/// <summary>
/// Permission 控制器
/// </summary>
public class PermissionController : BaseAppController<long, PermissionDto, IPermissionService, PermissionQueryCriteria>
{
    private readonly IAuthService _authService;
    private readonly ICurrentUserContext _currentUserContext;
    private readonly IPermissionService _permissionService;

    public PermissionController(IServiceProvider serviceProvider, IAuthService authService,
        ICurrentUserContext currentUserContext, IPermissionService permissionService) : base(serviceProvider)
    {
        _authService = authService;
        _currentUserContext = currentUserContext;
        _permissionService = permissionService;
    }

    [HttpGet("validate-permission")]
    [IgnoreLogging]
    [PublicPermission]
    public async Task<bool> ValidatePermissionAsync(PermissionType permissionType, string permissionCode,
        HttpMethod? httpMethod = null, string? path = null, string? appCode = null)
    {
        return await _permissionService.ValidatePermissionAsync(_currentUserContext.GetCurrentUserId()!.Value,
            permissionType, permissionCode,
            httpMethod == null ? Constants.EnumToHttpMethodMap[httpMethod!.Value] : null, path,
            appCode);
    }


    // /// <summary>
    // /// 获取用户权限
    // /// </summary>
    // [HttpGet("permissions")]
    // public async Task<UserPermissionDto> GetUserPermissionsAsync()
    // {
    //     return await _authService.GetUserPermissionsAsync(_currentUserContext.GetCurrentUserId()!.Value);
    // }

    // /// <summary>
    // /// 验证用户权限
    // /// </summary>
    // [HttpGet("validate-permission")]
    // public async Task<bool> ValidatePermissionAsync([FromQuery] long userId, [FromQuery] string permissionCode, [FromQuery] long? appId = null)
    // {
    //     return await _authService.ValidatePermissionAsync(userId, permissionCode, appId);
    // }
}