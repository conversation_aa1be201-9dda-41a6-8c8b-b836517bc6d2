using XJ.Framework.Library.Domain.Shared.Enums;
using XJ.Framework.Rbac.Domain.Shared.Enums;

namespace XJ.Framework.Rbac.EntityFrameworkCore.Repositories;

/// <summary>
/// UserPosition 仓储实现
/// </summary>
public class UserPositionRepository : BaseSoftDeleteRepository<RbacDbContext, long, UserPositionEntity>,
    IUserPositionRepository
{
    public UserPositionRepository(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }
}