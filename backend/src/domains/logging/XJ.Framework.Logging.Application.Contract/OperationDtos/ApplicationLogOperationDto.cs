
namespace XJ.Framework.Logging.Application.Contract.OperationDtos;

/// <summary>
/// ApplicationLog 操作 DTO
/// </summary>
public class ApplicationLogOperationDto : BaseOperationDto
{
    /// <summary>
    /// 应用名称
    /// </summary>
    public string Application { get; set; } = null!;

    /// <summary>
    /// 日志分类
    /// </summary>
    public string? Category { get; set; }

    /// <summary>
    /// 日志级别
    /// </summary>
    public string Level { get; set; } = null!;

    /// <summary>
    /// 客户端IP
    /// </summary>
    public string? ClientIp { get; set; }

    /// <summary>
    /// 控制器
    /// </summary>
    public string? Controller { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    public string? SourceContext { get; set; }

    /// <summary>
    /// 动作/方法
    /// </summary>
    public string? Action { get; set; }

    /// <summary>
    /// 路由模板
    /// </summary>
    public string? RouteTemplate { get; set; }

    /// <summary>
    /// 当前用户
    /// </summary>
    public string? CurrentUser { get; set; }

    /// <summary>
    /// 关联ID/链路追踪ID
    /// </summary>
    public string? CorrelationId { get; set; }

    /// <summary>
    /// 日志时间
    /// </summary>
    public DateTimeOffset Timestamp { get; set; }

    /// <summary>
    /// 日志内容
    /// </summary>
    public string Message { get; set; } = null!;

    /// <summary>
    /// 异常堆栈
    /// </summary>
    public string? Exception { get; set; }

} 
