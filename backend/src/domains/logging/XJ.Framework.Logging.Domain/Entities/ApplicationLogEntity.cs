
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace XJ.Framework.Logging.Domain.Entities;

/// <summary>
/// ApplicationLog 实体
/// </summary>
[Table("application_log", Schema = "dbo")]
public class ApplicationLogEntity : BaseEntity<long>
{
    /// <summary>
    /// 应用名称
    /// </summary>
    [Column("application")]
    [StringLength(40)]
    public required string Application { get; set; } = null!;

    /// <summary>
    /// 日志分类
    /// </summary>
    [Column("category")]
    [StringLength(100)]
    public string? Category { get; set; }

    /// <summary>
    /// 日志级别
    /// </summary>
    [Column("level")]
    [StringLength(40)]
    public required string Level { get; set; } = null!;

    /// <summary>
    /// 客户端IP
    /// </summary>
    [Column("client_ip")]
    [StringLength(100)]
    public string? ClientIp { get; set; }

    /// <summary>
    /// 控制器
    /// </summary>
    [Column("controller")]
    [StringLength(100)]
    public string? Controller { get; set; }

    /// <summary>
    /// 来源
    /// </summary>
    [Column("source_context")]
    [StringLength(1000)]
    public string? SourceContext { get; set; }

    /// <summary>
    /// 动作/方法
    /// </summary>
    [Column("action")]
    [StringLength(100)]
    public string? Action { get; set; }

    /// <summary>
    /// 路由模板
    /// </summary>
    [Column("route_template")]
    [StringLength(200)]
    public string? RouteTemplate { get; set; }

    /// <summary>
    /// 当前用户
    /// </summary>
    [Column("current_user")]
    [StringLength(100)]
    public string? CurrentUser { get; set; }

    /// <summary>
    /// 关联ID/链路追踪ID
    /// </summary>
    [Column("correlation_id")]
    [StringLength(100)]
    public string? CorrelationId { get; set; }

    /// <summary>
    /// 日志时间
    /// </summary>
    [Column("timestamp")]
    public required DateTimeOffset Timestamp { get; set; }

    /// <summary>
    /// 日志内容
    /// </summary>
    [Column("message")]
    [StringLength(-1)]
    public required string Message { get; set; } = null!;

    /// <summary>
    /// 异常堆栈
    /// </summary>
    [Column("exception")]
    [StringLength(-1)]
    public string? Exception { get; set; }

} 
