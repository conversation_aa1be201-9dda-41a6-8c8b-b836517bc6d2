namespace XJ.Framework.Logging.EntityFrameworkCore;

public class LoggingDbContext : BaseDbContext
{
    public LoggingDbContext(DbContextOptions options, IConfiguration configuration,
        IOptions<DatabaseOption> databaseOptions) : base(options, databaseOptions)
    {
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        optionsBuilder
            .UseSqlServer(DatabaseOptions.Value["Logging"]!.ConnectionString)
            .EnableSensitiveDataLogging().LogTo(Console.WriteLine);
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Initialize();

        modelBuilder.AddPropertyComments();

        modelBuilder.ApplyIndexAttributes();

        modelBuilder.Seed();

        // 配置枚举转换
        modelBuilder.ConfigureEnumToIntConversions();
    }
}
