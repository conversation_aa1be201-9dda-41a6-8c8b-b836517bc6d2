namespace XJ.Framework.Logging.WebApi.Mgt.Controllers;

/// <summary>
/// ApplicationLog 控制器
/// </summary>
public class ApplicationLogController : BaseAppController<long, ApplicationLogDto, IApplicationLogService,
    ApplicationLogQueryCriteria>
{
    public ApplicationLogController(IServiceProvider serviceProvider) : base(serviceProvider)
    {
    }


    [HttpGet("/page")]
    public async Task<PageDtoData<long, ApplicationLogDto>> GetPageAsync(
        [FromQuery] PagedQueryCriteria<ApplicationLogQueryCriteria> criteria)
    {
        return await Service.GetPageAsync(criteria);
    }
}
