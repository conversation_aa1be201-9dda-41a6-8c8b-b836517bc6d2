using System;

namespace XJ.Framework.Library.Cache.Abstraction;

/// <summary>
/// 缓存选项
/// </summary>
public class CacheOptions
{
    /// <summary>
    /// 缓存实例名称
    /// </summary>
    public string InstanceName { get; set; } = "default";

    /// <summary>
    /// 默认过期时间
    /// </summary>
    public TimeSpan DefaultExpiration { get; set; } = TimeSpan.FromMinutes(30);

    /// <summary>
    /// 是否启用滑动过期
    /// </summary>
    public bool EnableSlidingExpiration { get; set; }

    /// <summary>
    /// 滑动过期时间
    /// </summary>
    public TimeSpan? SlidingExpiration { get; set; }

    /// <summary>
    /// 是否启用缓存压缩
    /// </summary>
    public bool EnableCompression { get; set; }

    /// <summary>
    /// 键前缀
    /// </summary>
    public string KeyPrefix { get; set; } = string.Empty;

    /// <summary>
    /// Redis 连接字符串
    /// </summary>
    public string? ConnectionString { get; set; }

    /// <summary>
    /// Redis 数据库编号
    /// </summary>
    public int Database { get; set; } = 0;

    /// <summary>
    /// 缓存类型
    /// </summary>
    public string CacheType { get; set; } = "memory";
}
