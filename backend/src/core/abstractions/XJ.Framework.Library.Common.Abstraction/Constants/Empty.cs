namespace XJ.Framework.Library.Common.Abstraction.Constants
{
    /// <summary>
    /// 各种默认的空集合
    /// </summary>
    public static class Empty
    {
        public static readonly string[] StringArray = System.Array.Empty<string>();

        public static readonly Guid[] GuidArray = System.Array.Empty<Guid>();

        public static readonly List<string> StringList = System.Array.Empty<string>().ToList();
    }
}