namespace XJ.Framework.Library.Common.Abstraction.Telemetry;

/// <summary>
/// 遥测指标记录的提供者，用于记录各种指标
/// </summary>
public interface ITelemetryProvider
{
    /// <summary>
    /// 记录一个指标值
    /// </summary>
    /// <param name="name"></param>
    /// <param name="value"></param>
    /// <param name="properties"></param>
    void TraceMetric(string name, double value, IDictionary<string, string>? properties = null);
}