namespace XJ.Framework.Library.Common.Abstraction.Exceptions;

/// <summary>
/// 不可重试异常
/// </summary>
public class NonTransientException : ApplicationException
{
    /// <summary>
    /// NonTransientException的缺省构造函数
    /// </summary>
    /// <remarks>SystemSupportException的缺省构造函数.
    /// </remarks>
    public NonTransientException()
    {
    }

    /// <summary>
    /// NonTransientException的带错误消息参数的构造函数
    /// </summary>
    /// <param name="strMessage">错误消息串</param>
    /// <remarks>SystemSupportException的带错误消息参数的构造函数,该错误消息将在消息抛出异常时显示出来。
    /// </remarks>
    public NonTransientException(string strMessage)
        : base(strMessage)
    {
    }

    /// <summary>
    /// NonTransientException的构造函数。
    /// </summary>
    /// <param name="strMessage">错误消息串</param>
    /// <param name="ex">导致该异常的异常</param>
    /// <remarks>该构造函数把导致该异常的异常记录了下来。
    /// </remarks>
    public NonTransientException(string strMessage, Exception? ex)
        : base(strMessage, ex)
    {
    }
}