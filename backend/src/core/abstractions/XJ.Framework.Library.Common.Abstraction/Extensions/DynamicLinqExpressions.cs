using System.Linq.Expressions;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class DynamicLinqExpressions
{
    private class ReplaceParameterVisitor : ExpressionVisitor
    {
        private readonly ParameterExpression _oldParameter;
        private readonly ParameterExpression _newParameter;

        public ReplaceParameterVisitor(ParameterExpression oldParameter, ParameterExpression newParameter)
        {
            _oldParameter = oldParameter;
            _newParameter = newParameter;
        }

        protected override Expression VisitParameter(ParameterExpression node)
        {
            return node == _oldParameter ? _newParameter : base.VisitParameter(node);
        }
    }

    public static Expression<Func<T, bool>> True<T>()
    {
        return f => true;
    }

    public static Expression<Func<T, bool>> False<T>()
    {
        return f => false;
    }

    // public static Expression<Func<T, bool>> Or<T>(this Expression<Func<T, bool>> expr1, Expression<Func<T, bool>> expr2)
    // {
    //     var parameter = expr1.Parameters[0];
    //     var visitor = new ReplaceParameterVisitor(expr2.Parameters[0], parameter);
    //     var replacedBody = visitor.Visit(expr2.Body);
    //     var combinedBody = Expression.Or(expr1.Body, replacedBody);
    //     expr1 = Expression.Lambda<Func<T, bool>>(combinedBody, parameter);
    //     return expr1;
    // }

    public static Expression<Func<T, bool>> Or<T>(this Expression<Func<T, bool>> expr1,
        Expression<Func<T, bool>> expr2)
    {
        var invokedExpr = Expression.Invoke(expr2, expr1.Parameters.Cast<Expression>());
        return Expression.Lambda<Func<T, bool>>
            (Expression.Or(expr1.Body, invokedExpr), expr1.Parameters);
    }

    public static Expression<Func<T, bool>> And<T>(this Expression<Func<T, bool>> expr1,
        Expression<Func<T, bool>> expr2)
    {
        var invokedExpr = Expression.Invoke(expr2, expr1.Parameters.Cast<Expression>());
        return Expression.Lambda<Func<T, bool>>
            (Expression.And(expr1.Body, invokedExpr), expr1.Parameters);
    }

    // public static Expression<Func<T, bool>> And<T>(this Expression<Func<T, bool>> expr1,
    //     Expression<Func<T, bool>> expr2)
    // {
    //     var parameter = expr1.Parameters[0];
    //     var visitor = new ReplaceParameterVisitor(expr2.Parameters[0], parameter);
    //     var replacedBody = visitor.Visit(expr2.Body);
    //     var combinedBody = Expression.And(expr1.Body, replacedBody);
    //     expr1 = Expression.Lambda<Func<T, bool>>(combinedBody, parameter);
    //     return expr1;
    // }


    public static Expression<Func<T, bool>> AndLessThan<T, TProperty>(this Expression<Func<T, bool>> expr1,
        Expression<Func<T, TProperty>> left, TProperty value)
    {
        return expr1.And(LessThan(left, value));
    }

    public static Expression<Func<T, bool>> LessThan<T, TProperty>(Expression<Func<T, TProperty>> left, TProperty value)
    {
        var parameter = left.Parameters[0];
        var constant = Expression.Constant(value, typeof(TProperty));
        var lessThan = Expression.LessThan(left.Body, constant);

        return Expression.Lambda<Func<T, bool>>(lessThan, parameter);
    }

    public static Expression<Func<T, bool>> AndLessThanOrEqual<T, TProperty>(
        this Expression<Func<T, bool>> expr1,
        Expression<Func<T, TProperty>> left,
        TProperty value)
    {
        return expr1.And(LessThanOrEqual(left, value));
    }

    public static Expression<Func<T, bool>> LessThanOrEqual<T, TProperty>(
        Expression<Func<T, TProperty>> left,
        TProperty value)
    {
        var parameter = left.Parameters[0];
        var constant = Expression.Constant(value, typeof(TProperty));
        var lessThanOrEqual = Expression.LessThanOrEqual(left.Body, constant);
        return Expression.Lambda<Func<T, bool>>(lessThanOrEqual, parameter);
    }

    public static Expression<Func<T, bool>> AndNotEqualTo<T, TProperty>(
        this Expression<Func<T, bool>> expr1,
        Expression<Func<T, TProperty>> left,
        TProperty value)
    {
        return expr1.And(NotEqualTo(left, value));
    }

    public static Expression<Func<T, bool>> NotEqualTo<T, TProperty>(
        Expression<Func<T, TProperty>> left,
        TProperty value)
    {
        var parameter = left.Parameters[0];
        var constant = Expression.Constant(value, typeof(TProperty));
        var notEqual = Expression.NotEqual(left.Body, constant);
        return Expression.Lambda<Func<T, bool>>(notEqual, parameter);
    }

    public static Expression<Func<T, bool>> AndNotContains<T>(
        this Expression<Func<T, bool>> expr1,
        Expression<Func<T, string>> left,
        string value)
    {
        return expr1.And(NotContains(left, value));
    }

    /// <summary>
    /// 不包含查询（字符串）
    /// </summary>
    public static Expression<Func<T, bool>> NotContains<T>(
        Expression<Func<T, string>> left,
        string value)
    {
        var parameter = left.Parameters[0];
        var constant = Expression.Constant(value, typeof(string));
        var method = typeof(string).GetMethod("Contains", new[] { typeof(string) });
        var contains = Expression.Call(left.Body, method!, constant);
        var notContains = Expression.Not(contains);
        return Expression.Lambda<Func<T, bool>>(notContains, parameter);
    }

    public static Expression<Func<T, bool>> AndEqualTo<T, TProperty>(
        this Expression<Func<T, bool>> expr1,
        Expression<Func<T, TProperty>> left,
        TProperty value)
    {
        return expr1.And(EqualTo(left, value));
    }

    /// <summary>
    /// 等于比较
    /// </summary>
    public static Expression<Func<T, bool>> EqualTo<T, TProperty>(
        Expression<Func<T, TProperty>> left,
        TProperty value)
    {
        var parameter = left.Parameters[0];
        var constant = Expression.Constant(value, typeof(TProperty));
        var equal = Expression.Equal(left.Body, constant);
        return Expression.Lambda<Func<T, bool>>(equal, parameter);
    }

    public static Expression<Func<T, bool>> AndGreaterThan<T, TProperty>(
        this Expression<Func<T, bool>> expr1,
        Expression<Func<T, TProperty>> left,
        TProperty value)
    {
        return expr1.And(GreaterThan(left, value));
    }

    /// <summary>
    /// 大于比较
    /// </summary>
    public static Expression<Func<T, bool>> GreaterThan<T, TProperty>(
        Expression<Func<T, TProperty>> left,
        TProperty value)
    {
        var parameter = left.Parameters[0];
        var constant = Expression.Constant(value, typeof(TProperty));
        var greaterThan = Expression.GreaterThan(left.Body, constant);
        return Expression.Lambda<Func<T, bool>>(greaterThan, parameter);
    }

    public static Expression<Func<T, bool>> AndGreaterThanOrEqual<T, TProperty>(
        this Expression<Func<T, bool>> expr1,
        Expression<Func<T, TProperty>> left,
        TProperty value)
    {
        return expr1.And(GreaterThanOrEqual(left, value));
    }

    /// <summary>
    /// 大于等于比较
    /// </summary>
    public static Expression<Func<T, bool>> GreaterThanOrEqual<T, TProperty>(
        Expression<Func<T, TProperty>> left,
        TProperty value)
    {
        var parameter = left.Parameters[0];
        var constant = Expression.Constant(value, typeof(TProperty));
        var greaterThanOrEqual = Expression.GreaterThanOrEqual(left.Body, constant);
        return Expression.Lambda<Func<T, bool>>(greaterThanOrEqual, parameter);
    }

    public static Expression<Func<T, bool>> AndContains<T>(
        this Expression<Func<T, bool>> expr1,
        Expression<Func<T, string>> left,
        string value)
    {
        return expr1.And(Contains(left, value));
    }

    /// <summary>
    /// 包含查询（字符串）
    /// </summary>
    public static Expression<Func<T, bool>> Contains<T>(
        Expression<Func<T, string>> left,
        string value)
    {
        var parameter = left.Parameters[0];
        var constant = Expression.Constant(value, typeof(string));
        var method = typeof(string).GetMethod("Contains", new[] { typeof(string) });
        var contains = Expression.Call(left.Body, method!, constant);
        return Expression.Lambda<Func<T, bool>>(contains, parameter);
    }

    public static Expression<Func<T, bool>> AndStartsWith<T>(
        this Expression<Func<T, bool>> expr1,
        Expression<Func<T, string>> left,
        string value)
    {
        return expr1.And(StartsWith(left, value));
    }

    /// <summary>
    /// 以...开始
    /// </summary>
    public static Expression<Func<T, bool>> StartsWith<T>(
        Expression<Func<T, string>> left,
        string value)
    {
        var parameter = left.Parameters[0];
        var constant = Expression.Constant(value, typeof(string));
        var method = typeof(string).GetMethod("StartsWith", new[] { typeof(string) });
        var startsWith = Expression.Call(left.Body, method!, constant);
        return Expression.Lambda<Func<T, bool>>(startsWith, parameter);
    }

    public static Expression<Func<T, bool>> AndEndsWith<T>(
        this Expression<Func<T, bool>> expr1,
        Expression<Func<T, string>> left,
        string value)
    {
        return expr1.And(EndsWith(left, value));
    }

    /// <summary>
    /// 以...结束
    /// </summary>
    public static Expression<Func<T, bool>> EndsWith<T>(
        Expression<Func<T, string>> left,
        string value)
    {
        var parameter = left.Parameters[0];
        var constant = Expression.Constant(value, typeof(string));
        var method = typeof(string).GetMethod("EndsWith", new[] { typeof(string) });
        var endsWith = Expression.Call(left.Body, method!, constant);
        return Expression.Lambda<Func<T, bool>>(endsWith, parameter);
    }

    public static Expression<Func<T, bool>> AndBetween<T, TProperty>(
        this Expression<Func<T, bool>> expr1,
        Expression<Func<T, TProperty>> left,
        TProperty lower,
        TProperty upper) where TProperty : IComparable<TProperty>
    {
        return expr1.And(Between(left, lower, upper));
    }

    /// <summary>
    /// 在...之间
    /// </summary>
    public static Expression<Func<T, bool>> Between<T, TProperty>(
        Expression<Func<T, TProperty>> left,
        TProperty lower,
        TProperty upper) where TProperty : IComparable<TProperty>
    {
        var parameter = left.Parameters[0];
        var lowerConstant = Expression.Constant(lower, typeof(TProperty));
        var upperConstant = Expression.Constant(upper, typeof(TProperty));

        var greaterThanOrEqual = Expression.GreaterThanOrEqual(left.Body, lowerConstant);
        var lessThanOrEqual = Expression.LessThanOrEqual(left.Body, upperConstant);
        var between = Expression.AndAlso(greaterThanOrEqual, lessThanOrEqual);

        return Expression.Lambda<Func<T, bool>>(between, parameter);
    }

    public static Expression<Func<T, bool>> AndIn<T, TProperty>(
        this Expression<Func<T, bool>> expr1,
        Expression<Func<T, TProperty>> left,
        IEnumerable<TProperty> values)
    {
        return expr1.And(In(left, values));
    }

    /// <summary>
    /// 在集合中
    /// </summary>
    public static Expression<Func<T, bool>> In<T, TProperty>(
        Expression<Func<T, TProperty>> left,
        IEnumerable<TProperty> values)
    {
        var parameter = left.Parameters[0];
        var constant = Expression.Constant(values.ToList(), typeof(List<TProperty>));
        var method = typeof(List<TProperty>).GetMethod("Contains", new[] { typeof(TProperty) })!;
        var contains = Expression.Call(constant, method, left.Body);

        return Expression.Lambda<Func<T, bool>>(contains, parameter);
    }
}
