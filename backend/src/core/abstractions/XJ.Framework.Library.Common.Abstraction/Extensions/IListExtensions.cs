using System.Collections;

namespace XJ.Framework.Library.Common.Abstraction.Extensions;

public static class IListExtensions
{
    /// <summary>
    /// 从一个list中复制出一页数据
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <typeparam name="TTargetList"></typeparam>
    /// <param name="source"></param>
    /// <param name="pageIndex"></param>
    /// <param name="pageSize"></param>
    /// <returns></returns>
    public static TTargetList GetPage<T, TTargetList>(this IList<T> source, int pageIndex, int pageSize)
        where TTargetList : IList<T>, new()
    {
        return source.CopyTo(new TTargetList(), (pageIndex - 1) * pageSize, pageSize);
    }

    public static TTargetList CopyTo<T, TTargetList>(this IList<T> source, TTargetList dest, int startIndex, int len)
        where TTargetList : IList<T>
    {
        source.NullCheck();
        dest.NullCheck();

        if (startIndex < source.Count)
        {
            for (var i = startIndex; i < Math.Min(source.Count, startIndex + len); i++)
                dest.Add((T)source[i]!);
        }

        return dest;
    }

    /// <summary>
    /// 按照对象的内置比较器进行快排序
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <typeparam name="TList"></typeparam>
    /// <param name="list"></param>
    public static void QuickSort<T, TList>(this IList list) where T : IComparable<T>
    {
        list.NullCheck();

        Comparison<T> comparison = (left, right) => left.CompareTo(right);

        InnerQuickSort(list, 0, list.Count - 1, comparison);
    }

    /// <summary>
    /// 按照一定的规则进行快速排序
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="list"></param>
    /// <param name="comparison"></param>
    public static void QuickSort<T>(this IList list, Comparison<T> comparison)
    {
        list.NullCheck();
        comparison.NullCheck();

        InnerQuickSort(list, 0, list.Count - 1, comparison);
    }

    /// <summary>
    /// 按照一定的规则进行快速排序
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="list"></param>
    /// <param name="comparer"></param>
    public static void QuickSort<T>(this IList list, IComparer<T> comparer)
    {
        list.NullCheck();
        comparer.NullCheck();

        Comparison<T> comparison = comparer.Compare;

        InnerQuickSort(list, 0, list.Count - 1, comparison);
    }

    private static void InnerQuickSort<T>(IList list, int left, int right, Comparison<T> comparison)
    {
        if (left < right)
        {
            var middle = GetMiddleFromQuickSort(list, left, right, comparison);

            InnerQuickSort(list, left, middle - 1, comparison);
            InnerQuickSort(list, middle + 1, right, comparison);
        }
    }

    private static int GetMiddleFromQuickSort<T>(IList list, int left, int right, Comparison<T> comparison)
    {
        var key = (T)list[left]!;

        while (left < right)
        {
            while (left < right && comparison(key, (T)list[right]!) < 0)
                right--;

            if (left < right)
            {
                var temp = (T)list[left]!;
                list[left] = list[right];
                list[right] = temp;
                left++;
            }

            while (left < right && comparison(key, (T)list[left]!) > 0)
                left++;

            if (left < right)
            {
                var temp = (T)list[right]!;
                list[right] = list[left];
                list[left] = temp;
                right--;
            }

            list[left] = key;
        }

        return left;
    }
}