using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Expressions;

public sealed partial class ExpressionParser
{
    /// <summary>
    /// 得到字符串段（按照标识符分割）
    /// </summary>
    /// <param name="expression"></param>
    /// <param name="throwParseException"></param>
    /// <returns></returns>
    public static ExpressionSegmentCollection GetSegments(string expression, bool throwParseException = true)
    {
        var pr = Parse(expression, throwParseException);

        ExpressionSegmentCollection segments = [];

        if (expression.IsNotEmpty() && pr.Identifiers != null)
        {
            List<ParseIdentifier> sortedIds = pr.Identifiers.GetSortedByPositionIdentifiers();

            var i = 0;

            foreach (var id in sortedIds)
            {
                if (i < id.Position)
                {
                    segments.Add(new ExpressionSegment
                    {
                        Position = i,
                        Content = expression.Substring(i, id.Position - i)
                    });

                    i = id.Position;
                }

                segments.Add(new ExpressionSegment
                {
                    Position = i,
                    Content = expression.Substring(i, id.Identifier.Length),
                    Identifier = id
                });

                i += id.Identifier.Length;
            }

            if (i < expression.Length)
                segments.Add(new ExpressionSegment
                {
                    Position = i,
                    Content = expression.Substring(i, expression.Length - i)
                });
        }

        return segments;
    }

    /// <summary>
    /// 分析表达式，
    /// </summary>
    /// <param name="expression">表达式</param>
    /// <param name="throwParseException">是否抛出分析的异常，默认为 true </param>
    /// <returns>分析结果</returns>
    /// <remarks>
    /// 对传入的表达式进行分析
    /// <code source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Expression\ExpressionParserTest.cs" region="parse" lang="cs" title="调用分析表达式的函数" />
    /// </remarks>
    public static ParseResult Parse(string expression, bool throwParseException = true)
    {
        ParseResult result;

        if (string.IsNullOrEmpty(expression) == false)
        {
            ParsingContext context = new(expression)
            {
                OutputIdentifiers = true,
                ThrowParseException = throwParseException
            };
            var tree = ExpressionParser.instance.DoExpression(context);

            if (context.CurrentChar != '\0')
            {
                if (context.ThrowParseException)
                    throw ParsingException.NewParsingException(ParseError.peInvalidOperator, context.Position,
                        context.CurrentChar.ToString());
            }

            result = new ParseResult(tree, context.Identifiers);
        }
        else
            result = new ParseResult(null, null);

        return result;
    }

    /// <summary>
    /// 计算表达式，直接获得结果
    /// </summary>
    /// <param name="expression">表达式</param>
    /// <param name="options"></param>
    /// <returns>运算结果</returns>
    /// <remarks>
    /// 直接计算出表达式的结果
    /// </remarks>
    public static object? Calculate(string expression, UserFunctionCalculationOptions? options = null)
    {
        object? result = null;

        options ??= UserFunctionCalculationOptions.Default();

        var pr = Parse(expression);

        if (pr != null)
            result = GetTreeValue(pr.Tree, options);

        return result;
    }

    /// <summary>
    /// 计算表达式，直接获得结果
    /// </summary>
    /// <param name="expression">表达式</param>
    /// <param name="options"></param>
    /// <returns>运算结果</returns>
    /// <remarks>
    /// 直接计算出表达式的结果
    /// </remarks>
    public static object? Calculate(string expression, BuiltInFunctionsCalculationOptions options)
    {
        options.NullCheck();

        object? result = null;

        var pr = Parse(expression);

        if (pr != null)
            result = GetTreeValue(pr.Tree, options);

        return result;
    }

    /// <summary>
    /// 根据语法解析完的Tree，计算出结果
    /// </summary>
    /// <param name="tree">语法解析树</param>
    /// <param name="options">包含自定义函数的计算参数</param>
    /// <returns>结果返回值</returns>
    /// <remarks>
    /// 计算解析出的二叉树，得到运算结果
    /// </remarks>
    public static object? GetTreeValue(ExpTreeNode? tree, UserFunctionCalculationOptions options)
    {
        options.NullCheck();

        return ExpTreeExecutor.GetValue(tree, options);
    }

    public static object? GetTreeValue(ExpTreeNode? tree, BuiltInFunctionsCalculationOptions options)
    {
        options.NullCheck();

        return ExpTreeExecutor.GetValue(tree, options);
    }
}