namespace XJ.Framework.Library.Common.Abstraction.Expressions;

/// <summary>
/// 用户自定义函数传入的参数
/// </summary>
/// <remarks>
/// 用户传入的参数对象
/// <code source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Expression\ExpressionParserTest.cs" region="UserDefinedFucntions" lang="cs" />
/// </remarks>
public sealed class ParamObject
{
    private object? paramValue = null;
    private int position = -1;
    private int paramIndex = 0;

    internal ParamObject(object? v, int nPos, int paramIndex)
    {
        this.paramValue = v;
        this.position = nPos;
        this.paramIndex = paramIndex;
    }

    /// <summary>
    /// 参数值
    /// </summary>
    public object? Value {
        get { return this.paramValue; }
        internal set { this.paramValue = Value; }
    }

    /// <summary>
    /// 参数在表达式中的位置
    /// </summary>
    public int Position {
        get { return this.position; }
        internal set { this.position = value; }
    }

    /// <summary>
    /// 是第几个参数
    /// </summary>
    public int ParamIndex {
        get { return this.paramIndex; }
        internal set { this.paramIndex = value; }
    }

    /// <summary>
    /// 检查参数的类型
    /// </summary>
    /// <typeparam name="T">参数是否是指定类型，或者是它的派生类</typeparam>
    /// <remarks>
    /// 检查参数类型是否为指定类型
    /// <code source="..\Framework\TestProjects\DeluxeWorks.Library.Test\Expression\ExpressionParserTest.cs" region="UserDefinedFucntions" lang="cs" />
    /// </remarks>
    public void CheckParameterType<T>()
    {
        if (this.Value != null)
        {
            var expectedType = typeof(T);

            if (false == (this.Value.GetType().IsSubclassOf(expectedType) ||
                          this.Value.GetType() == expectedType ||
                          expectedType.IsAssignableFrom(this.Value.GetType())))
                throw ParsingException.NewParsingException(ParseError.InvalidParameterType, 0, expectedType.Name,
                    this.Value.GetType().Name);
        }
    }
}