using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Validation;

/// <summary>
/// 自定义校验器属性的抽象基类
/// </summary>
public abstract class BaseValidatorAttribute : Attribute
{
    private string ruleset = string.Empty;
    private string messageTemplate = string.Empty;
    private string tag = string.Empty;

    private HashSet<string> ruleSets = new HashSet<string>();

    /// <summary>
    /// 
    /// </summary>
    protected BaseValidatorAttribute()
    {
        this.RebuildRuleSets(this.ruleset);
    }

    /// <summary>
    /// 翻译时使用的类别
    /// </summary>
    public string Category {
        get;
        set;
    } = string.Empty;

    /// <summary>
    /// 校验规则集合
    /// </summary>
    public string Ruleset {
        get {
            return this.ruleset != null ? this.ruleset : string.Empty;
        }
        set {
            this.RebuildRuleSets(value);
            this.ruleset = value;
        }
    }

    /// <summary>
    /// 校验器附加的标签
    /// </summary>
    public string Tag {
        get {
            return this.tag;
        }
        set {
            this.tag = value;
        }
    }

    /// <summary>
    /// 校验器上附加的校验失败信息
    /// </summary>
    public string MessageTemplate {
        get {
            return this.messageTemplate;
        }
        set {
            this.messageTemplate = value;
        }
    }

    /// <summary>
    /// 是否包含某个ruleset
    /// </summary>
    /// <param name="ruleSetArray"></param>
    /// <returns></returns>
    public bool ContainsRuleset(params string[] ruleSetArray)
    {
        var result = false;

        if (ruleSetArray != null)
        {
            foreach (var item in ruleSetArray)
            {
                if (this.ruleSets.Contains(item))
                {
                    result = true;
                    break;
                }
            }
        }

        return result;
    }

    private void RebuildRuleSets(string ruleset)
    {
        this.ruleSets.Clear();

        List<string> parts = SplitRuleSet(ruleset);

        foreach (var part in parts)
        {
            var trimmed = part.Trim();

            if (this.ruleSets.Contains(trimmed) == false)
                this.ruleSets.Add(trimmed);
        }
    }

    private static List<string> SplitRuleSet(string ruleset)
    {
        List<string> rulesetList = new List<string>();

        if (ruleset.IsNotEmpty())
        {
            string[] parts = ruleset.Split(',');

            foreach (var part in parts)
            {
                var trimmed = part.Trim();

                rulesetList.Add(trimmed);
            }
        }
        else
            rulesetList.Add(string.Empty);

        return rulesetList;
    }
}