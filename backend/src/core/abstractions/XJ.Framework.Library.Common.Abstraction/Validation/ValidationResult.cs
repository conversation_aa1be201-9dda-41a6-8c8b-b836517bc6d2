namespace XJ.Framework.Library.Common.Abstraction.Validation;

/// <summary>
/// 校验器校验结果
/// </summary>
/// <remarks>
/// 定义校验器校验结果的数据结构
/// </remarks>
[Serializable]
public sealed class ValidationResult
{
    private readonly string message;
    private readonly string? key;
    private readonly string tag;

    [NonSerialized] private readonly object target;

    [NonSerialized] private readonly Validator validator;
    private readonly IEnumerable<ValidationResult> nestedValidationResults;
    private static readonly IEnumerable<ValidationResult> NoNestedValidationResults = new ValidationResult[0];

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">校验显示信息</param>
    /// <param name="target">校验对象</param>
    /// <param name="key">校验结果标识</param>
    /// <param name="tag">校验结果标记</param>
    /// <param name="validator">校验器</param>
    public ValidationResult(string message, object target, string? key, string tag, Validator validator)
        : this(message, target, key, tag, validator, ValidationResult.NoNestedValidationResults)
    {
    }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="message">校验信息</param>
    /// <param name="target">校验对象</param>
    /// <param name="key">校验结果标识</param>
    /// <param name="tag">校验结果标记</param>
    /// <param name="validator">本次校验的校验器</param>
    /// <param name="nestedValidationResults">嵌套的校验结果（应用场景：或型的校验器，通常在这个结构内记录多个校验失败的信息）</param>
    public ValidationResult(string message, object target, string? key, string tag, Validator validator,
        IEnumerable<ValidationResult> nestedValidationResults)
    {
        this.message = message;
        this.key = key;
        this.target = target;
        this.tag = tag;
        this.validator = validator;
        this.nestedValidationResults = nestedValidationResults;
    }

    /// <summary>
    /// 标识
    /// </summary>
    public string? Key {
        get {
            return this.key;
        }
    }

    /// <summary>
    /// 校验信息
    /// </summary>
    public string Message {
        get {
            return this.message;
        }
    }

    /// <summary>
    /// 标记
    /// </summary>
    public string Tag {
        get {
            return tag;
        }
    }

    /// <summary>
    /// 被校验对象
    /// </summary>
    public object Target {
        get {
            return this.target;
        }
    }

    /// <summary>
    /// 翻译时使用的类别
    /// </summary>
    public string Category {
        get;
        internal set;
    } = string.Empty;

    /// <summary>
    /// 校验器
    /// </summary>
    public Validator Validator {
        get {
            return this.validator;
        }
    }

    /// <summary>
    /// 嵌套的校验结果
    /// </summary>
    public IEnumerable<ValidationResult> NestedValidationResults {
        get {
            return this.nestedValidationResults;
        }
    }
}