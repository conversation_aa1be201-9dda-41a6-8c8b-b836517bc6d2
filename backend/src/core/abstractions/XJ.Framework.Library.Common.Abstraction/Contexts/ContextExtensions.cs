using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Contexts;

public static partial class ContextExtensions
{
    public static T GetInstance<T>(this Dictionary<System.Type, ContextCacheQueueBase> cacheDict)
        where T : ContextCacheQueueBase
    {
        cacheDict.NullCheck(nameof(cacheDict));

        var type = typeof(T);
        ContextCacheQueueBase? instance;

        lock (cacheDict)
        {
            if (cacheDict.TryGetValue(type, out instance) == false)
            {
                instance = (ContextCacheQueueBase?)Activator.CreateInstance(typeof(T), true);

                (instance != null).FalseThrow($"不能创建类型\"{typeof(T).FullName}\"的实例");

                cacheDict.Add(type, instance!);
            }
        }

        return (T)instance!;
    }

    public static ObjectContextCache Objects(this IContextContainer container)
    {
        container.NullCheck(nameof(container));

        return container.GetInstance<ObjectContextCache>();
    }

    public static StringContextCache Strings(this IContextContainer container)
    {
        container.NullCheck(nameof(container));

        return container.GetInstance<StringContextCache>();
    }

    /// <summary>
    /// 得到操作上下文缓存
    /// </summary>
    /// <param name="container"></param>
    /// <returns></returns>
    public static ActionContextCache ActionContexts(this IContextContainer container)
    {
        container.NullCheck(nameof(container));

        return container.GetInstance<ActionContextCache>();
    }

    /// <summary>
    /// 得到操作上下文
    /// </summary>
    /// <typeparam name="TActionContext"></typeparam>
    /// <param name="container"></param>
    /// <returns></returns>
    public static TActionContext ActionContext<TActionContext>(this IContextContainer container)
        where TActionContext : ActionContextBase<TActionContext>, new()
    {
        var result = (TActionContext?)container.ActionContexts().GetOrAddNewValue(
            GetAndCheckContextKey<TActionContext>(),
            (cache, key) =>
            {
                TActionContext context = new();
                cache.Add(key, context);

                return context;
            });

        return result!;
    }

    /// <summary>
    /// 初始化CorrelationId。如果在当前上下文中没有CorrelationId，则使用 defaultValue。
    /// 如果 defaultValue为空，则随机生成一个。
    /// </summary>
    /// <param name="container"></param>
    /// <param name="defaultValue"></param>
    /// <returns></returns>
    public static string InitCorrelationId(this IContextContainer container, string? defaultValue = "")
    {
        return container.GetCorrelationId(defaultValue);
    }

    /// <summary>
    /// 获得CorrelationId。如果在当前上下文中没有CorrelationId，则使用 defaultValue。
    /// 如果 defaultValue为空，则随机生成一个。
    /// </summary>
    /// <param name="container"></param>
    /// <param name="defaultValue"></param>
    /// <returns></returns>
    public static string GetCorrelationId(this IContextContainer container, string? defaultValue = "")
    {
        string? correlationId;

        if (container.Strings().TryGetValue("__CorrelationId", out correlationId) == false)
        {
            correlationId = defaultValue.IsNotEmpty() ? defaultValue : Guid.NewGuid().ToString();

            container.Strings().Add("__CorrelationId", correlationId!);
        }

        return correlationId!;
    }

    private static string GetAndCheckContextKey<TActionContext>()
    {
        var attr =
            (ActionContextDescriptionAttribute?)Attribute.GetCustomAttribute(typeof(TActionContext),
                typeof(ActionContextDescriptionAttribute));

        (attr != null).FalseThrow("不能在类{0}上找到ActionContextDescriptionAttribute的定义",
            typeof(TActionContext).AssemblyQualifiedName!);

        (attr!.Key.IsNotEmpty()).FalseThrow("类{0}上ActionContextDescriptionAttribute的Key属性不能为空",
            typeof(TActionContext).AssemblyQualifiedName!);

        return attr.Key;
    }
}