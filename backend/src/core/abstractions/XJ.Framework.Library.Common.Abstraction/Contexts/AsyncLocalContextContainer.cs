using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Contexts;

public class AsyncLocalContextContainer : IContextContainer
{
    private static AsyncLocal<Dictionary<System.Type, ContextCacheQueueBase>> _AsyncCacheDictionary
        = new AsyncLocal<Dictionary<Type, ContextCacheQueueBase>>();

    /// <summary>
    /// 初始化上下文
    /// </summary>
    /// <returns></returns>
    public IContextContainer Init()
    {
        if (_AsyncCacheDictionary.Value == null)
            _AsyncCacheDictionary.Value = new Dictionary<Type, ContextCacheQueueBase>();
        else
            this.Clear();

        return this;
    }

    /// <summary>
    /// Clear操作不仅删除所有数据，而且会创建一个新的Async状态。不继承上一级的
    /// </summary>
    public void Clear()
    {
        if (_AsyncCacheDictionary.Value != null)
        {
            _AsyncCacheDictionary.Value.Clear();
        }
    }

    public T GetInstance<T>() where T : ContextCacheQueueBase
    {
        (_AsyncCacheDictionary.Value != null).FalseThrow<InvalidOperationException>(
            "需要为AsyncLocalContextContainer执行Init()");

        return _AsyncCacheDictionary.Value!.GetInstance<T>();
    }
}