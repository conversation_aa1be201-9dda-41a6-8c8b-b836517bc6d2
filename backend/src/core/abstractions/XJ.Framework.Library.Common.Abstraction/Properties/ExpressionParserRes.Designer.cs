//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace XJ.Framework.Library.Common.Abstraction.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    internal class ExpressionParserRes {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal ExpressionParserRes() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("MCS.Library.Common.Abstractions.Properties.ExpressionParserRes", typeof(ExpressionParserRes).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        internal static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 函数{0}错误，{1}.
        /// </summary>
        internal static string FunctionError {
            get {
                return ResourceManager.GetString("FunctionError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to &quot;{0}&quot;不是DateDiff所支持的时间间隔类型.
        /// </summary>
        internal static string InvalidDateDiffType {
            get {
                return ResourceManager.GetString("InvalidDateDiffType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 第{0}的参数类型错误，应该是{1}类型.
        /// </summary>
        internal static string InvalidParameterType {
            get {
                return ResourceManager.GetString("InvalidParameterType", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 参数个数应该不少于{0}个.
        /// </summary>
        internal static string InvalidParamsCount {
            get {
                return ResourceManager.GetString("InvalidParamsCount", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 应该有一个&quot;{0}&quot;.
        /// </summary>
        internal static string peCharExpected {
            get {
                return ResourceManager.GetString("peCharExpected", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 出现浮点运算越界或除零错误.
        /// </summary>
        internal static string peFloatOverflow {
            get {
                return ResourceManager.GetString("peFloatOverflow", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 出现格式错误.
        /// </summary>
        internal static string peFormatError {
            get {
                return ResourceManager.GetString("peFormatError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 函数错误.
        /// </summary>
        internal static string peFuncError {
            get {
                return ResourceManager.GetString("peFuncError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 存在非法字符&quot;{0}&quot;.
        /// </summary>
        internal static string peInvalidChar {
            get {
                return ResourceManager.GetString("peInvalidChar", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 存在非法操作符&quot;{0}&quot;.
        /// </summary>
        internal static string peInvalidOperator {
            get {
                return ResourceManager.GetString("peInvalidOperator", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 函数&quot;{0}&quot;应该有{1}个参数.
        /// </summary>
        internal static string peInvalidParam {
            get {
                return ResourceManager.GetString("peInvalidParam", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 存在非法字符串.
        /// </summary>
        internal static string peInvalidString {
            get {
                return ResourceManager.GetString("peInvalidString", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 用户自定义函数返回值不能为空.
        /// </summary>
        internal static string peInvalidUFValue {
            get {
                return ResourceManager.GetString("peInvalidUFValue", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 需要操作数.
        /// </summary>
        internal static string peNeedOperand {
            get {
                return ResourceManager.GetString("peNeedOperand", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 在语法错误.
        /// </summary>
        internal static string peSyntaxError {
            get {
                return ResourceManager.GetString("peSyntaxError", resourceCulture);
            }
        }
        
        /// <summary>
        ///   Looks up a localized string similar to 在第{0}个字符附近.
        /// </summary>
        internal static string position {
            get {
                return ResourceManager.GetString("position", resourceCulture);
            }
        }
    }
}
