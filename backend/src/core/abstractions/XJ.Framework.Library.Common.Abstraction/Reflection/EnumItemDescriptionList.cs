using System.Collections.ObjectModel;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Common.Abstraction.Reflection;

/// <summary>
/// 强类型的枚举项描述信息集合
/// </summary>
/// <remarks>
/// 强类型的枚举项描述信息集合,该信息是只读的
/// </remarks>
public sealed class EnumItemDescriptionList : KeyedCollection<string, EnumItemDescriptionAttribute>
{
    /// <summary>
    /// 带枚举项描述信息类参数的构造函数
    /// </summary>
    /// <param name="other"></param>
    public EnumItemDescriptionList(IEnumerable<EnumItemDescriptionAttribute> other)
    {
        this.CopyFrom(other);
    }

    /// <summary>
    /// 获得指定位置元素的枚举项描述信息
    /// </summary>
    /// <param name="enumItem">枚举项目</param>
    /// <returns>枚举项描述信息类的实例</returns>
    /// <remarks>该属性是只读的
    /// </remarks>
    public EnumItemDescriptionAttribute this[System.Enum enumItem] {
        get {
            return base[enumItem.ToString()];
        }
    }

    public EnumItemDescriptionAttribute? FindByName(string name, bool ignoreCase = false)
    {
        return this.FirstOrDefault(attr => string.Compare(attr.Name, name, ignoreCase) == 0);
    }

    public EnumItemDescriptionAttribute? FindByShortName(string name, bool ignoreCase = false)
    {
        return this.FirstOrDefault(attr => string.Compare(attr.ShortName, name, ignoreCase) == 0);
    }

    protected override string GetKeyForItem(EnumItemDescriptionAttribute item)
    {
        return item.EnumName;
    }
}