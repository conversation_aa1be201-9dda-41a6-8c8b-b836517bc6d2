<Project Sdk="Microsoft.NET.Sdk">


    <Import Project="..\..\..\..\Common.Secrets.props"/>
    <Import Project="..\..\..\..\Common.props"/>

    <ItemGroup>
        <Compile Remove="Class1.cs"/>
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\abstractions\XJ.Framework.Library.Excel.Abstraction\XJ.Framework.Library.Excel.Abstraction.csproj"/>
    </ItemGroup>


    <ItemGroup>
        <PackageReference Include="DotNetCore.NPOI"/>
        <PackageReference Include="DotNetCore.NPOI.Core"/>
        <PackageReference Include="SharpZipLib"/>
    </ItemGroup>
</Project>
