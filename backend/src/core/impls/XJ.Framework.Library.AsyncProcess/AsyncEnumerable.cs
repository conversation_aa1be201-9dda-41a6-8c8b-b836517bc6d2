using System.Threading.Channels;

namespace XJ.Framework.Library.AsyncProcess
{
    public class AsyncEnumerable : IAsyncEnumerable<string>
    {
        readonly Action _disposeEvent;
        readonly ChannelReader<string> _channel;

        public AsyncEnumerable(ChannelReader<string> channel, Action disposeEvent)
        {
            this._disposeEvent = disposeEvent;
            this._channel = channel;
        }

        public IAsyncEnumerator<string> GetAsyncEnumerator(CancellationToken cancellationToken = default)
        {
            return new AsyncEnumerator(_disposeEvent, _channel, cancellationToken);
        }

        /// <summary>
        /// Consume all result and wait complete asynchronously.
        /// </summary>
        public async Task WaitAsync(CancellationToken cancellationToken = default)
        {
            await foreach (var _ in this.WithCancellation(cancellationToken).ConfigureAwait(false))
            {
            }
        }

        /// <summary>
        /// Returning first value and wait complete asynchronously.
        /// </summary>
        public async Task<string> FirstAsync(CancellationToken cancellationToken = default)
        {
            string? data = null;
            await foreach (var item in this.WithCancellation(cancellationToken).ConfigureAwait(false))
            {
                data ??= item;
            }

            if (data == null)
            {
                throw new InvalidOperationException("Process does not return any data.");
            }
            else
            {
                return data;
            }
        }

        /// <summary>
        /// Returning first value or null and wait complete asynchronously.
        /// </summary>
        public async Task<string?> FirstOrDefaultAsync(CancellationToken cancellationToken = default)
        {
            string? data = null;
            await foreach (var item in this.WithCancellation(cancellationToken).ConfigureAwait(false))
            {
                data ??= item;
            }

            return data;
        }

        public async Task<string[]> ToTask(CancellationToken cancellationToken = default)
        {
            var list = new List<string>();
            await foreach (var item in this.WithCancellation(cancellationToken).ConfigureAwait(false))
            {
                list.Add(item);
            }

            return list.ToArray();
        }

        /// <summary>
        /// Write the all received data to console.
        /// </summary>
        public async Task WriteLineAllAsync(CancellationToken cancellationToken = default)
        {
            await foreach (var item in this.WithCancellation(cancellationToken).ConfigureAwait(false))
            {
                Console.WriteLine(item);
            }
        }
    }
}