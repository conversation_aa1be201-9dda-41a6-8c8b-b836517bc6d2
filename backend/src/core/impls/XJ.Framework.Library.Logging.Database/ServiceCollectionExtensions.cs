using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.Configuration;
using Serilog;
using Serilog.Sinks.MSSqlServer;
using System.Data;
using XJ.Framework.Library.Common.Abstraction.Configuration;
using XJ.Framework.Library.Logging.Abstraction;

namespace XJ.Framework.Library.Logging.Database;

public static class ServiceCollectionExtensions
{
    public static WebApplication UseDatabaseLoggingProvider<TWrapper>(this WebApplication app)
        where TWrapper : class
    {
        Serilog.Debugging.SelfLog.Enable(msg => System.Diagnostics.Debug.WriteLine(msg));

        var loggerConfiguration = new ConfigurationBuilder()
            .AddSolutionJsonFile($"database.{app.Environment.EnvironmentName}.json")
            .AddSolutionJsonFile($"logging.{app.Environment.EnvironmentName}.json")
            .AddJsonFile("appsettings.json")
            .AddJsonFile($"appsettings.{app.Environment.EnvironmentName}.json")
            .AddUserSecrets<TWrapper>()
            .AddEnvironmentVariables()
            .Build();

        var loggerConfig = new LoggerConfiguration()
            .ReadFrom.Configuration(loggerConfiguration);

        var usings = loggerConfiguration.GetSection("Serilog").GetSection("Using").Get<List<string>>();
        if (usings?.Contains("Serilog.Sinks.MSSqlServer") ?? false)
        {
            var dbConnections = loggerConfiguration.GetSection("Database")
                .GetChildren()
                .ToDictionary(
                    x => x["Name"]!,
                    x => x["ConnectionString"]!);


            var connectionString = dbConnections.TryGetValue("Logging", out var connection)
                ? connection
                : throw new InvalidOperationException("Logging connection string not found in configuration.");


            var additionalColumns = new List<SqlColumn>
            {
                // 应用程序名称
                new SqlColumn("application", SqlDbType.NVarChar, false, 20)
                {
                    PropertyName = "Application"
                },
                // 日志分类
                new SqlColumn("category", SqlDbType.NVarChar, true, 50)
                {
                    PropertyName = "Category"
                },
                // 客户端IP
                new SqlColumn("client_ip", SqlDbType.NVarChar, true, 50)
                {
                    PropertyName = "ClientIp"
                },
                // 控制器
                new SqlColumn("controller", SqlDbType.NVarChar, true, 50)
                {
                    PropertyName = "Controller"
                },
                // 动作
                new SqlColumn("action", SqlDbType.NVarChar, true, 50)
                {
                    PropertyName = "Action"
                },
                // 路由模板
                new SqlColumn("route_template", SqlDbType.NVarChar, true, 100)
                {
                    PropertyName = "RouteTemplate"
                },
                // 当前用户
                new SqlColumn("current_user", SqlDbType.NVarChar, true, 50)
                {
                    PropertyName = "CurrentUser"
                },
                // 关联ID
                new SqlColumn("correlation_id", SqlDbType.NVarChar, true, 50)
                {
                    PropertyName = "CorrelationId"
                },
                new SqlColumn("source_context", SqlDbType.NVarChar, true, 500)
                {
                    PropertyName = "SourceContext"
                },
            };


            loggerConfig
                .Enrich.WithUtcTimestamp(app.Services)
                .WriteTo.MSSqlServer(
                    connectionString: connectionString,
                    sinkOptions: new MSSqlServerSinkOptions()
                    {
                        TableName = "application_log",
                        AutoCreateSqlTable = true
                    },
                    columnOptions: new ColumnOptions()
                    {
                        Store = new List<StandardColumn>
                        {
                            StandardColumn.Level,
                            StandardColumn.TimeStamp,
                            StandardColumn.Message,
                            StandardColumn.Exception
                        },
                        Id = { ColumnName = "id", DataType = SqlDbType.BigInt, AllowNull = false },
                        TimeStamp = { ColumnName = "timestamp", DataType = SqlDbType.DateTimeOffset },
                        Level =
                        {
                            ColumnName = "level", DataType = SqlDbType.NVarChar, AllowNull = false, StoreAsEnum = false,
                            DataLength = 50
                        },
                        Exception =
                        {
                            ColumnName = "exception", DataType = SqlDbType.NVarChar, AllowNull = true, DataLength = -1
                        },
                        Message =
                        {
                            ColumnName = "message", DataType = SqlDbType.NVarChar, AllowNull = false, DataLength = -1
                        },
                        AdditionalColumns = additionalColumns,
                    }
                );
        }

        Log.Logger = loggerConfig.CreateLogger();

        return app;
    }
}
