using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Modular.Abstractions;
using XJ.Framework.Library.Modular.ApiClients;
using XJ.Framework.Library.Modular.Core;

namespace XJ.Framework.Library.Modular.Extensions;

/// <summary>
/// 模块化Web应用程序构建器扩展
/// </summary>
public static class ModularWebApplicationBuilderExtensions
{
    /// <summary>
    /// 添加模块化架构核心服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddModularArchitecture(this IServiceCollection services)
    {
        // 注册核心服务
        services.AddSingleton<ModuleRegistry>();
        services.AddSingleton<ApiClientCallConfiguration>();
        services.AddSingleton<SmartApiClientFactory>();

        return services;
    }


    /// <summary>
    /// 配置API客户端调用策略
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureAction">配置操作</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection ConfigureApiClients(
        this IServiceCollection services,
        Action<ApiClientCallConfiguration> configureAction)
    {
        // 确保核心服务已注册
        if (!services.Any(s => s.ServiceType == typeof(ApiClientCallConfiguration)))
        {
            services.AddModularArchitecture();
        }

        // 获取配置实例并应用配置
        var serviceProvider = services.BuildServiceProvider();
        var configuration = serviceProvider.GetRequiredService<ApiClientCallConfiguration>();
        configureAction(configuration);

        return services;
    }

    /// <summary>
    /// 添加智能API客户端
    /// </summary>
    /// <typeparam name="TApiClient">API客户端类型</typeparam>
    /// <param name="services">服务集合</param>
    /// <param name="strategy">调用策略</param>
    /// <param name="httpEndpoint">HTTP端点</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddSmartApiClient<TApiClient>(
        this IServiceCollection services,
        CallStrategy strategy = CallStrategy.Auto,
        string? httpEndpoint = null) where TApiClient : class
    {
        // 注册API客户端工厂（如果尚未注册）
        if (!services.Any(s => s.ServiceType == typeof(SmartApiClientFactory)))
        {
            services.AddSingleton<SmartApiClientFactory>();
        }

        // 注册API客户端
        services.AddTransient<TApiClient>(serviceProvider =>
        {
            var factory = serviceProvider.GetRequiredService<SmartApiClientFactory>();
            return factory.CreateClient<TApiClient>();
        });

        return services;
    }

    /// <summary>
    /// 使用模块化架构
    /// </summary>
    /// <param name="builder">Web应用程序构建器</param>
    /// <returns>模块化应用程序构建器</returns>
    public static ModularWebApplicationBuilder UseModularArchitecture(this WebApplicationBuilder builder)
    {
        return new ModularWebApplicationBuilder(builder);
    }
}

/// <summary>
/// 模块化Web应用程序构建器
/// </summary>
public class ModularWebApplicationBuilder
{
    private readonly WebApplicationBuilder _builder;
    private readonly ModuleRegistry _moduleRegistry;
    private readonly ApiClientCallConfiguration _apiClientConfiguration;
    private readonly List<IModule> _modules = new();

    public ModularWebApplicationBuilder(WebApplicationBuilder builder)
    {
        _builder = builder;
        _apiClientConfiguration = new ApiClientCallConfiguration();

        // 注册核心服务
        _builder.Services.AddLogging();

        // 创建临时服务提供者来获取Logger
        var tempProvider = _builder.Services.BuildServiceProvider();
        var logger = tempProvider.GetRequiredService<ILogger<ModuleRegistry>>();
        _moduleRegistry = new ModuleRegistry(logger);

        // 注册ModuleRegistry为单例
        _builder.Services.AddSingleton(_moduleRegistry);

        // 注册性能监控（可选）
        _builder.Services.AddSingleton<XJ.Framework.Library.Modular.Monitoring.PerformanceMonitor>();
    }

    /// <summary>
    /// 添加模块
    /// </summary>
    /// <typeparam name="TModule">模块类型</typeparam>
    /// <typeparam name="TAuthProvider">认证提供者类型</typeparam>
    /// <typeparam name="TAuthInfoGetter">认证信息获取器类型</typeparam>
    /// <returns>模块化应用程序构建器</returns>
    public ModularWebApplicationBuilder AddModule<TModule, TAuthProvider, TAuthInfoGetter>()
        where TModule : IModule, new()
        where TAuthProvider : class, IAuthProvider
        where TAuthInfoGetter : class, IAuthInfoGetter
    {
        var module = new TModule();
        return AddModule<TAuthProvider, TAuthInfoGetter>(module);
    }

    /// <summary>
    /// 添加模块
    /// </summary>
    /// <typeparam name="TAuthProvider">认证提供者类型</typeparam>
    /// <typeparam name="TAuthInfoGetter">认证信息获取器类型</typeparam>
    /// <param name="module">模块实例</param>
    /// <returns>模块化应用程序构建器</returns>
    public ModularWebApplicationBuilder AddModule<TAuthProvider, TAuthInfoGetter>(IModule module)
        where TAuthProvider : class, IAuthProvider
        where TAuthInfoGetter : class, IAuthInfoGetter
    {
        _modules.Add(module);
        _moduleRegistry.RegisterModule(module);

        try
        {
            // 根据模块类型配置相应的WebApi
            if (module.Descriptor.IsManagement)
            {
                module.ConfigureManagementWebApi<TAuthProvider, TAuthInfoGetter>(_builder);
            }
            else
            {
                module.ConfigureRegularWebApi<TAuthProvider, TAuthInfoGetter>(_builder);
            }
        }
        catch (NotSupportedException ex)
        {
            // 某些模块可能不支持特定的站点类型，记录警告但继续
            var loggerFactory = _builder.Services.BuildServiceProvider().GetService<ILoggerFactory>();
            var logger = loggerFactory?.CreateLogger("ModularWebApplicationBuilder");
            logger?.LogWarning(ex, "Module {ModuleId} does not support the requested site type", module.Descriptor.Id);
        }

        return this;
    }

    /// <summary>
    /// 配置API客户端调用策略
    /// </summary>
    /// <param name="configureAction">配置操作</param>
    /// <returns>模块化应用程序构建器</returns>
    public ModularWebApplicationBuilder ConfigureApiClients(Action<ApiClientCallConfiguration> configureAction)
    {
        configureAction(_apiClientConfiguration);
        return this;
    }

    /// <summary>
    /// 添加智能API客户端
    /// </summary>
    /// <typeparam name="TApiClient">API客户端类型</typeparam>
    /// <param name="strategy">调用策略</param>
    /// <param name="httpEndpoint">HTTP端点（当策略为Http时使用）</param>
    /// <returns>模块化应用程序构建器</returns>
    public ModularWebApplicationBuilder AddSmartApiClient<TApiClient>(
        CallStrategy strategy = CallStrategy.Auto,
        string? httpEndpoint = null) where TApiClient : class
    {
        _apiClientConfiguration.Configure<TApiClient>(strategy, httpEndpoint);

        // 注册API客户端工厂
        _builder.Services.AddTransient<TApiClient>(serviceProvider =>
        {
            var factory = serviceProvider.GetRequiredService<SmartApiClientFactory>();
            return factory.CreateClient<TApiClient>();
        });

        return this;
    }

    /// <summary>
    /// 构建并运行应用程序
    /// </summary>
    /// <returns>任务</returns>
    public async Task BuildAndRunAsync()
    {
        var app = Build();
        await app.RunAsync();
    }

    /// <summary>
    /// 构建应用程序
    /// </summary>
    /// <returns>Web应用程序</returns>
    public WebApplication Build()
    {
        // 创建模块聚合器
        var tempProvider = _builder.Services.BuildServiceProvider();
        var logger = tempProvider.GetRequiredService<ILogger<ModuleAggregator>>();
        var aggregator = new ModuleAggregator(_moduleRegistry, logger);

        // 验证模块配置
        aggregator.ValidateConfiguration();

        // 配置服务
        aggregator.ConfigureServices(_builder.Services, _builder.Configuration, _apiClientConfiguration);

        // 构建应用程序
        var app = _builder.Build();

        // 配置应用程序
        aggregator.ConfigureApplication(app);

        // 记录统计信息
        LogModuleStatistics(app, aggregator);

        return app;
    }

    /// <summary>
    /// 记录模块统计信息
    /// </summary>
    /// <param name="app">Web应用程序</param>
    /// <param name="aggregator">模块聚合器</param>
    private void LogModuleStatistics(WebApplication app, ModuleAggregator aggregator)
    {
        var logger = app.Services.GetRequiredService<ILogger<ModularWebApplicationBuilder>>();
        var statistics = aggregator.GetStatistics();

        logger.LogInformation("=== Modular Application Statistics ===");
        logger.LogInformation("Total Modules: {TotalModules}", statistics.TotalModules);
        logger.LogInformation("Management Modules: {ManagementModules}", statistics.ManagementModules);
        logger.LogInformation("Regular Modules: {RegularModules}", statistics.RegularModules);
        logger.LogInformation("Controller Assemblies: {ControllerAssemblies}", statistics.ControllerAssemblies);
        logger.LogInformation("API Client Mappings: {ApiClientMappings}", statistics.ApiClientMappings);

        logger.LogInformation("=== Module Details ===");
        foreach (var module in statistics.ModuleDetails)
        {
            logger.LogInformation("Module: {ModuleId} ({ModuleName}) v{Version} - {ModuleType}",
                module.Id, module.Name, module.Version,
                module.IsManagement ? "Management" : "Regular");

            if (module.Dependencies.Any())
            {
                logger.LogInformation("  Dependencies: {Dependencies}", string.Join(", ", module.Dependencies));
            }

            logger.LogInformation("  Controller Assemblies: {ControllerAssemblies}, API Mappings: {ApiMappings}",
                module.ControllerAssemblies, module.ApiClientMappings);
        }

        logger.LogInformation("=== Modular Application Ready ===");
    }
}

/// <summary>
/// Web应用程序扩展
/// </summary>
public static class WebApplicationExtensions
{
    /// <summary>
    /// 获取模块统计信息
    /// </summary>
    /// <param name="app">Web应用程序</param>
    /// <returns>模块统计信息</returns>
    public static ModuleStatistics GetModuleStatistics(this WebApplication app)
    {
        var moduleRegistry = app.Services.GetRequiredService<ModuleRegistry>();
        var logger = app.Services.GetRequiredService<ILogger<ModuleAggregator>>();
        var aggregator = new ModuleAggregator(moduleRegistry, logger);

        return aggregator.GetStatistics();
    }

    /// <summary>
    /// 获取已注册的模块列表
    /// </summary>
    /// <param name="app">Web应用程序</param>
    /// <returns>模块列表</returns>
    public static IEnumerable<IModule> GetRegisteredModules(this WebApplication app)
    {
        var moduleRegistry = app.Services.GetRequiredService<ModuleRegistry>();
        return moduleRegistry.GetAllModules();
    }
}
