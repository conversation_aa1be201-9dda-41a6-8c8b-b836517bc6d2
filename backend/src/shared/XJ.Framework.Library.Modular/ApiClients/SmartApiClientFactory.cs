using System.Diagnostics;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using XJ.Framework.Library.Modular.Core;
using XJ.Framework.Library.Modular.Exceptions;
using XJ.Framework.Library.Modular.Monitoring;

namespace XJ.Framework.Library.Modular.ApiClients;

/// <summary>
/// 调用策略
/// </summary>
public enum CallStrategy
{
    /// <summary>
    /// 自动选择（默认）
    /// </summary>
    Auto,
    
    /// <summary>
    /// 强制使用内存调用
    /// </summary>
    InMemory,
    
    /// <summary>
    /// 强制使用HTTP调用
    /// </summary>
    Http
}

/// <summary>
/// API客户端调用配置
/// </summary>
public class ApiClientCallConfiguration
{
    private readonly Dictionary<string, CallStrategy> _strategies = new();
    private readonly Dictionary<string, string> _httpEndpoints = new();
    
    /// <summary>
    /// 配置API客户端调用策略
    /// </summary>
    /// <typeparam name="TApiClient">API客户端类型</typeparam>
    /// <param name="strategy">调用策略</param>
    /// <param name="httpEndpoint">HTTP端点（当策略为Http时使用）</param>
    public ApiClientCallConfiguration Configure<TApiClient>(CallStrategy strategy, string? httpEndpoint = null)
    {
        var clientTypeName = typeof(TApiClient).Name;
        _strategies[clientTypeName] = strategy;
        
        if (strategy == CallStrategy.Http && !string.IsNullOrEmpty(httpEndpoint))
        {
            _httpEndpoints[clientTypeName] = httpEndpoint;
        }
        
        return this;
    }
    
    /// <summary>
    /// 获取API客户端调用策略
    /// </summary>
    /// <param name="clientTypeName">客户端类型名称</param>
    /// <returns>调用策略</returns>
    public CallStrategy GetStrategy(string clientTypeName)
    {
        return _strategies.TryGetValue(clientTypeName, out var strategy) ? strategy : CallStrategy.Auto;
    }
    
    /// <summary>
    /// 获取HTTP端点
    /// </summary>
    /// <param name="clientTypeName">客户端类型名称</param>
    /// <returns>HTTP端点</returns>
    public string? GetHttpEndpoint(string clientTypeName)
    {
        return _httpEndpoints.TryGetValue(clientTypeName, out var endpoint) ? endpoint : null;
    }
}

/// <summary>
/// 智能API客户端工厂
/// </summary>
public class SmartApiClientFactory
{
    private readonly ModuleRegistry _moduleRegistry;
    private readonly IServiceProvider _serviceProvider;
    private readonly ApiClientCallConfiguration _configuration;
    private readonly ILogger<SmartApiClientFactory> _logger;
    private readonly PerformanceMonitor? _performanceMonitor;
    
    public SmartApiClientFactory(
        ModuleRegistry moduleRegistry,
        IServiceProvider serviceProvider,
        ApiClientCallConfiguration configuration,
        ILogger<SmartApiClientFactory> logger)
    {
        _moduleRegistry = moduleRegistry;
        _serviceProvider = serviceProvider;
        _configuration = configuration;
        _logger = logger;
        _performanceMonitor = serviceProvider.GetService<PerformanceMonitor>();
    }
    
    /// <summary>
    /// 创建API客户端
    /// </summary>
    /// <typeparam name="TApiClient">API客户端类型</typeparam>
    /// <returns>API客户端实例</returns>
    /// <exception cref="ApiClientCreationException">创建API客户端失败时抛出</exception>
    public TApiClient CreateClient<TApiClient>() where TApiClient : class
    {
        var clientTypeName = typeof(TApiClient).Name;
        var stopwatch = Stopwatch.StartNew();

        try
        {
            var strategy = DetermineCallStrategy(clientTypeName);

            _logger.LogDebug("Creating API client {ClientType} with strategy {Strategy}",
                clientTypeName, strategy);

            var client = strategy switch
            {
                CallStrategy.InMemory => CreateInMemoryClient<TApiClient>(),
                CallStrategy.Http => CreateHttpClient<TApiClient>(),
                CallStrategy.Auto => CreateAutoClient<TApiClient>(),
                _ => throw new ArgumentOutOfRangeException(nameof(strategy), strategy, null)
            };

            stopwatch.Stop();
            _logger.LogDebug("Successfully created API client {ClientType} in {Duration}ms",
                clientTypeName, stopwatch.ElapsedMilliseconds);

            return client;
        }
        catch (Exception ex)
        {
            stopwatch.Stop();
            _logger.LogError(ex, "Failed to create API client {ClientType} after {Duration}ms",
                clientTypeName, stopwatch.ElapsedMilliseconds);

            throw new ApiClientCreationException(clientTypeName,
                $"Failed to create API client of type {clientTypeName}", ex);
        }
    }
    
    /// <summary>
    /// 确定调用策略
    /// </summary>
    /// <param name="clientTypeName">客户端类型名称</param>
    /// <returns>调用策略</returns>
    private CallStrategy DetermineCallStrategy(string clientTypeName)
    {
        var configuredStrategy = _configuration.GetStrategy(clientTypeName);
        
        if (configuredStrategy != CallStrategy.Auto)
        {
            return configuredStrategy;
        }
        
        // 自动策略：检查目标模块是否在当前进程中
        var targetModuleId = _moduleRegistry.GetTargetModuleId(clientTypeName);
        if (!string.IsNullOrEmpty(targetModuleId) && _moduleRegistry.IsModuleRegistered(targetModuleId))
        {
            return CallStrategy.InMemory;
        }
        
        return CallStrategy.Http;
    }
    
    /// <summary>
    /// 创建内存调用客户端
    /// </summary>
    /// <typeparam name="TApiClient">API客户端类型</typeparam>
    /// <returns>API客户端实例</returns>
    private TApiClient CreateInMemoryClient<TApiClient>() where TApiClient : class
    {
        var clientTypeName = typeof(TApiClient).Name;
        var targetModuleId = _moduleRegistry.GetTargetModuleId(clientTypeName);
        
        if (string.IsNullOrEmpty(targetModuleId))
        {
            throw new InvalidOperationException(
                $"No target module mapping found for API client: {clientTypeName}");
        }
        
        // 创建内存调用的HttpClient
        var httpClient = new HttpClient(new InMemoryHttpHandler(targetModuleId, _serviceProvider));
        
        // 使用反射创建API客户端实例
        return CreateApiClientInstance<TApiClient>(httpClient);
    }
    
    /// <summary>
    /// 创建HTTP调用客户端
    /// </summary>
    /// <typeparam name="TApiClient">API客户端类型</typeparam>
    /// <returns>API客户端实例</returns>
    private TApiClient CreateHttpClient<TApiClient>() where TApiClient : class
    {
        var clientTypeName = typeof(TApiClient).Name;
        var httpEndpoint = _configuration.GetHttpEndpoint(clientTypeName);
        
        HttpClient httpClient;
        if (!string.IsNullOrEmpty(httpEndpoint))
        {
            // 使用配置的端点
            httpClient = new HttpClient { BaseAddress = new Uri(httpEndpoint) };
        }
        else
        {
            // 使用传统的HttpClient（从DI容器获取）
            var httpClientFactory = _serviceProvider.GetService<IHttpClientFactory>();
            if (httpClientFactory != null)
            {
                httpClient = httpClientFactory.CreateClient(clientTypeName);
            }
            else
            {
                httpClient = new HttpClient();
            }
        }
        
        return CreateApiClientInstance<TApiClient>(httpClient);
    }
    
    /// <summary>
    /// 创建自动选择客户端
    /// </summary>
    /// <typeparam name="TApiClient">API客户端类型</typeparam>
    /// <returns>API客户端实例</returns>
    private TApiClient CreateAutoClient<TApiClient>() where TApiClient : class
    {
        var strategy = DetermineCallStrategy(typeof(TApiClient).Name);
        return strategy == CallStrategy.InMemory ? CreateInMemoryClient<TApiClient>() : CreateHttpClient<TApiClient>();
    }
    
    /// <summary>
    /// 创建API客户端实例
    /// </summary>
    /// <typeparam name="TApiClient">API客户端类型</typeparam>
    /// <param name="httpClient">HTTP客户端</param>
    /// <returns>API客户端实例</returns>
    private TApiClient CreateApiClientInstance<TApiClient>(HttpClient httpClient) where TApiClient : class
    {
        try
        {
            // 尝试使用ActivatorUtilities创建实例，它会自动解析依赖
            return ActivatorUtilities.CreateInstance<TApiClient>(_serviceProvider, httpClient);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create API client instance of type {ClientType}", typeof(TApiClient).Name);
            throw new InvalidOperationException(
                $"Failed to create API client instance of type {typeof(TApiClient).Name}. " +
                $"Make sure the API client has a constructor that accepts HttpClient and other required services.", ex);
        }
    }
}
