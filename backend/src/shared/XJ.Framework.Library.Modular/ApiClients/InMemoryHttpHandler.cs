using System.Net;
using System.Text;
using System.Text.RegularExpressions;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

namespace XJ.Framework.Library.Modular.ApiClients;

/// <summary>
/// 内存HTTP处理器
/// 将HTTP请求转换为直接的控制器方法调用
/// </summary>
public class InMemoryHttpHandler : DelegatingHandler
{
    private readonly string _targetModuleId;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<InMemoryHttpHandler> _logger;
    
    public InMemoryHttpHandler(string targetModuleId, IServiceProvider serviceProvider)
    {
        _targetModuleId = targetModuleId;
        _serviceProvider = serviceProvider;
        _logger = serviceProvider.GetRequiredService<ILogger<InMemoryHttpHandler>>();
    }
    
    protected override async Task<HttpResponseMessage> SendAsync(
        HttpRequestMessage request, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Processing in-memory request for module {ModuleId}: {Method} {Uri}",
                _targetModuleId, request.Method, request.RequestUri);
            
            // 创建HTTP上下文
            var httpContext = await CreateHttpContextAsync(request);
            
            // 执行控制器调用
            await ExecuteControllerAsync(httpContext);
            
            // 构建响应
            return await BuildResponseAsync(httpContext);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing in-memory request for module {ModuleId}", _targetModuleId);
            return CreateErrorResponse(ex);
        }
    }
    
    /// <summary>
    /// 创建HTTP上下文
    /// </summary>
    /// <param name="request">HTTP请求</param>
    /// <returns>HTTP上下文</returns>
    private async Task<DefaultHttpContext> CreateHttpContextAsync(HttpRequestMessage request)
    {
        var httpContext = new DefaultHttpContext();
        var serviceScope = _serviceProvider.CreateScope();
        httpContext.RequestServices = serviceScope.ServiceProvider;
        
        // 设置请求信息
        httpContext.Request.Method = request.Method.Method;
        httpContext.Request.Path = request.RequestUri?.AbsolutePath ?? "/";
        httpContext.Request.QueryString = new QueryString(request.RequestUri?.Query ?? "");
        
        // 设置请求头
        foreach (var header in request.Headers)
        {
            httpContext.Request.Headers[header.Key] = header.Value.ToArray();
        }
        
        // 设置请求体
        if (request.Content != null)
        {
            var contentStream = await request.Content.ReadAsStreamAsync();
            httpContext.Request.Body = contentStream;
            
            var contentType = request.Content.Headers.ContentType?.ToString();
            if (!string.IsNullOrEmpty(contentType))
            {
                httpContext.Request.ContentType = contentType;
            }
            
            // 复制内容头
            foreach (var header in request.Content.Headers)
            {
                httpContext.Request.Headers[header.Key] = header.Value.ToArray();
            }
        }
        
        // 设置响应流
        httpContext.Response.Body = new MemoryStream();
        
        return httpContext;
    }
    
    /// <summary>
    /// 执行控制器调用
    /// </summary>
    /// <param name="httpContext">HTTP上下文</param>
    private async Task ExecuteControllerAsync(HttpContext httpContext)
    {
        // 获取MVC相关服务
        var actionDescriptorCollectionProvider = httpContext.RequestServices
            .GetRequiredService<IActionDescriptorCollectionProvider>();
        var actionInvokerFactory = httpContext.RequestServices
            .GetRequiredService<IActionInvokerFactory>();
        
        // 查找匹配的Action
        var actionDescriptor = FindMatchingAction(
            actionDescriptorCollectionProvider.ActionDescriptors.Items,
            httpContext.Request.Path,
            httpContext.Request.Method);
        
        if (actionDescriptor == null)
        {
            httpContext.Response.StatusCode = 404;
            httpContext.Response.ContentType = "application/json";
            var errorResponse = new { error = "Not Found", path = httpContext.Request.Path.Value };
            var json = System.Text.Json.JsonSerializer.Serialize(errorResponse);
            await httpContext.Response.WriteAsync(json);
            return;
        }
        
        _logger.LogDebug("Found matching action: {ActionName}", actionDescriptor.DisplayName);
        
        // 创建路由数据
        var routeData = ExtractRouteData(actionDescriptor, httpContext.Request.Path);
        
        // 创建Action上下文
        var actionContext = new ActionContext(httpContext, routeData, actionDescriptor);
        
        // 执行Action
        var invoker = actionInvokerFactory.CreateInvoker(actionContext);
        if (invoker != null)
        {
            await invoker.InvokeAsync();
        }
        else
        {
            httpContext.Response.StatusCode = 500;
            await httpContext.Response.WriteAsync("Failed to create action invoker");
        }
    }
    
    /// <summary>
    /// 查找匹配的Action
    /// </summary>
    /// <param name="actionDescriptors">Action描述符列表</param>
    /// <param name="path">请求路径</param>
    /// <param name="method">HTTP方法</param>
    /// <returns>匹配的Action描述符</returns>
    private ActionDescriptor? FindMatchingAction(
        IReadOnlyList<ActionDescriptor> actionDescriptors,
        string path,
        string method)
    {
        var candidates = new List<(ActionDescriptor Action, int Score)>();
        
        foreach (var action in actionDescriptors)
        {
            // 检查HTTP方法匹配
            if (!IsHttpMethodMatch(action, method))
                continue;
            
            // 检查路径匹配并计算匹配分数
            var score = CalculatePathMatchScore(action, path);
            if (score > 0)
            {
                candidates.Add((action, score));
            }
        }
        
        // 返回分数最高的匹配项
        return candidates.OrderByDescending(c => c.Score).FirstOrDefault().Action;
    }
    
    /// <summary>
    /// 检查HTTP方法是否匹配
    /// </summary>
    /// <param name="action">Action描述符</param>
    /// <param name="method">HTTP方法</param>
    /// <returns>是否匹配</returns>
    private bool IsHttpMethodMatch(ActionDescriptor action, string method)
    {
        var httpMethodMetadata = action.EndpointMetadata?
            .OfType<Microsoft.AspNetCore.Mvc.Routing.HttpMethodAttribute>()
            .FirstOrDefault();
        
        if (httpMethodMetadata == null)
            return true; // 如果没有指定HTTP方法，则匹配所有方法
        
        return httpMethodMetadata.HttpMethods.Contains(method, StringComparer.OrdinalIgnoreCase);
    }
    
    /// <summary>
    /// 计算路径匹配分数
    /// </summary>
    /// <param name="action">Action描述符</param>
    /// <param name="path">请求路径</param>
    /// <returns>匹配分数，0表示不匹配</returns>
    private int CalculatePathMatchScore(ActionDescriptor action, string path)
    {
        var routeTemplate = GetRouteTemplate(action);
        if (string.IsNullOrEmpty(routeTemplate))
            return 0;
        
        try
        {
            var pattern = ConvertRouteTemplateToRegex(routeTemplate);
            var regex = new Regex(pattern, RegexOptions.IgnoreCase);
            var match = regex.Match(path);
            
            if (!match.Success)
                return 0;
            
            // 计算匹配分数：精确匹配得分更高
            var score = 100;
            
            // 减少参数数量的分数（参数越少，匹配越精确）
            var parameterCount = Regex.Matches(routeTemplate, @"\{[^}]+\}").Count;
            score -= parameterCount * 10;
            
            // 增加路径长度的分数（更具体的路径得分更高）
            score += routeTemplate.Length;
            
            return score;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error calculating path match score for template {Template}", routeTemplate);
            return 0;
        }
    }
    
    /// <summary>
    /// 获取路由模板
    /// </summary>
    /// <param name="action">Action描述符</param>
    /// <returns>路由模板</returns>
    private string GetRouteTemplate(ActionDescriptor action)
    {
        // 优先使用AttributeRouteInfo
        if (action.AttributeRouteInfo?.Template != null)
        {
            return action.AttributeRouteInfo.Template;
        }
        
        // 构建传统路由
        var controllerName = action.RouteValues.TryGetValue("controller", out var controller) ? controller : "";
        var actionName = action.RouteValues.TryGetValue("action", out var actionValue) ? actionValue : "";
        
        if (!string.IsNullOrEmpty(controllerName) && !string.IsNullOrEmpty(actionName))
        {
            return $"api/{controllerName.ToLower()}/{actionName.ToLower()}";
        }
        
        return string.Empty;
    }
    
    /// <summary>
    /// 将路由模板转换为正则表达式
    /// </summary>
    /// <param name="routeTemplate">路由模板</param>
    /// <returns>正则表达式</returns>
    private string ConvertRouteTemplateToRegex(string routeTemplate)
    {
        var pattern = routeTemplate;
        
        // 替换参数占位符为捕获组
        pattern = Regex.Replace(pattern, @"\{([^}:]+)(?::[^}]+)?\}", @"([^/]+)");
        
        // 替换控制器和动作占位符
        pattern = pattern.Replace("[controller]", @"([^/]+)");
        pattern = pattern.Replace("[action]", @"([^/]+)");
        
        // 确保以 / 开头
        if (!pattern.StartsWith("/"))
            pattern = "/" + pattern;
        
        // 添加正则表达式标记
        return "^" + Regex.Escape(pattern).Replace(@"\([^/]+\)", @"([^/]+)") + "$";
    }
    
    /// <summary>
    /// 提取路由数据
    /// </summary>
    /// <param name="action">Action描述符</param>
    /// <param name="path">请求路径</param>
    /// <returns>路由数据</returns>
    private RouteData ExtractRouteData(ActionDescriptor action, string path)
    {
        var routeData = new RouteData();
        
        // 添加控制器和Action信息
        if (action.RouteValues.TryGetValue("controller", out var controller))
            routeData.Values["controller"] = controller;
        if (action.RouteValues.TryGetValue("action", out var actionValue))
            routeData.Values["action"] = actionValue;
        
        // 提取路由参数
        try
        {
            var routeTemplate = GetRouteTemplate(action);
            if (!string.IsNullOrEmpty(routeTemplate))
            {
                var parameters = ExtractRouteParameters(routeTemplate, path);
                foreach (var parameter in parameters)
                {
                    routeData.Values[parameter.Key] = parameter.Value;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error extracting route parameters");
        }
        
        return routeData;
    }
    
    /// <summary>
    /// 从路由模板和路径中提取参数
    /// </summary>
    /// <param name="routeTemplate">路由模板</param>
    /// <param name="path">请求路径</param>
    /// <returns>路由参数</returns>
    private Dictionary<string, object> ExtractRouteParameters(string routeTemplate, string path)
    {
        var parameters = new Dictionary<string, object>();
        
        try
        {
            // 找到路由模板中的参数占位符
            var parameterMatches = Regex.Matches(routeTemplate, @"\{([^}:]+)(?::[^}]+)?\}");
            var parameterNames = parameterMatches.Cast<Match>().Select(m => m.Groups[1].Value).ToList();
            
            if (parameterNames.Count == 0)
                return parameters;
            
            // 将路由模板转换为正则表达式
            var pattern = ConvertRouteTemplateToRegex(routeTemplate);
            var regex = new Regex(pattern, RegexOptions.IgnoreCase);
            var match = regex.Match(path);
            
            if (match.Success)
            {
                for (int i = 0; i < parameterNames.Count && i + 1 < match.Groups.Count; i++)
                {
                    var paramName = parameterNames[i];
                    var paramValue = match.Groups[i + 1].Value;
                    
                    // URL解码参数值
                    paramValue = Uri.UnescapeDataString(paramValue);
                    parameters[paramName] = paramValue;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Error extracting route parameters from template {Template} and path {Path}",
                routeTemplate, path);
        }
        
        return parameters;
    }
    
    /// <summary>
    /// 构建HTTP响应
    /// </summary>
    /// <param name="httpContext">HTTP上下文</param>
    /// <returns>HTTP响应消息</returns>
    private async Task<HttpResponseMessage> BuildResponseAsync(HttpContext httpContext)
    {
        var responseStream = (MemoryStream)httpContext.Response.Body;
        responseStream.Position = 0;
        
        var response = new HttpResponseMessage((HttpStatusCode)httpContext.Response.StatusCode);
        
        // 设置响应内容
        if (responseStream.Length > 0)
        {
            var responseContent = new byte[responseStream.Length];
            await responseStream.ReadExactlyAsync(responseContent, 0, (int)responseStream.Length);
            response.Content = new ByteArrayContent(responseContent);
        }
        
        // 复制响应头
        foreach (var header in httpContext.Response.Headers)
        {
            if (header.Key.Equals("Content-Type", StringComparison.OrdinalIgnoreCase))
            {
                response.Content?.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray());
            }
            else
            {
                response.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray());
            }
        }
        
        return response;
    }
    
    /// <summary>
    /// 创建错误响应
    /// </summary>
    /// <param name="exception">异常</param>
    /// <returns>错误响应</returns>
    private HttpResponseMessage CreateErrorResponse(Exception exception)
    {
        var response = new HttpResponseMessage(HttpStatusCode.InternalServerError);
        var errorContent = new
        {
            error = "Internal Server Error",
            message = exception.Message,
            moduleId = _targetModuleId,
            timestamp = DateTime.UtcNow
        };
        
        var json = System.Text.Json.JsonSerializer.Serialize(errorContent);
        response.Content = new StringContent(json, Encoding.UTF8, "application/json");
        
        return response;
    }
}
