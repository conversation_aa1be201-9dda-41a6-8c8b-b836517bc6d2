using System.Reflection;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.ApplicationParts;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using XJ.Framework.Library.Modular.Abstractions;
using XJ.Framework.Library.Modular.ApiClients;

namespace XJ.Framework.Library.Modular.Core;

/// <summary>
/// 模块聚合器
/// 负责将多个模块聚合到一个应用程序中
/// </summary>
public class ModuleAggregator
{
    private readonly ModuleRegistry _moduleRegistry;
    private readonly ILogger<ModuleAggregator> _logger;
    
    public ModuleAggregator(ModuleRegistry moduleRegistry, ILogger<ModuleAggregator> logger)
    {
        _moduleRegistry = moduleRegistry;
        _logger = logger;
    }
    
    /// <summary>
    /// 配置服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <param name="apiClientConfiguration">API客户端配置</param>
    public void ConfigureServices(
        IServiceCollection services, 
        IConfigurationRoot configuration,
        ApiClientCallConfiguration apiClientConfiguration)
    {
        _logger.LogInformation("Configuring services for {ModuleCount} modules", 
            _moduleRegistry.GetAllModules().Count());
        
        // 注册核心服务
        services.AddSingleton(_moduleRegistry);
        services.AddSingleton(apiClientConfiguration);
        services.AddSingleton<SmartApiClientFactory>();
        
        // 按依赖顺序配置模块服务
        var modules = _moduleRegistry.GetModulesInDependencyOrder();
        foreach (var module in modules)
        {
            _logger.LogDebug("Configuring services for module: {ModuleId}", module.Descriptor.Id);
            
            try
            {
                module.ConfigureServices(services, configuration);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error configuring services for module: {ModuleId}", module.Descriptor.Id);
                throw;
            }
        }
        
        // 配置控制器
        ConfigureControllers(services);
        
        _logger.LogInformation("Successfully configured services for all modules");
    }
    
    /// <summary>
    /// 配置应用程序
    /// </summary>
    /// <param name="app">Web应用程序</param>
    public void ConfigureApplication(WebApplication app)
    {
        _logger.LogInformation("Configuring application for {ModuleCount} modules", 
            _moduleRegistry.GetAllModules().Count());
        
        // 按依赖顺序配置模块应用程序
        var modules = _moduleRegistry.GetModulesInDependencyOrder();
        foreach (var module in modules)
        {
            _logger.LogDebug("Configuring application for module: {ModuleId}", module.Descriptor.Id);
            
            try
            {
                module.ConfigureApplication(app);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error configuring application for module: {ModuleId}", module.Descriptor.Id);
                throw;
            }
        }
        
        _logger.LogInformation("Successfully configured application for all modules");
    }
    
    /// <summary>
    /// 配置控制器
    /// </summary>
    /// <param name="services">服务集合</param>
    private void ConfigureControllers(IServiceCollection services)
    {
        _logger.LogDebug("Configuring controllers for aggregated modules");
        
        // 获取所有控制器程序集
        var controllerAssemblies = _moduleRegistry.GetControllerAssemblies().ToList();
        
        // 添加宿主程序集（入口程序集）
        var entryAssembly = Assembly.GetEntryAssembly();
        if (entryAssembly != null && !controllerAssemblies.Contains(entryAssembly))
        {
            controllerAssemblies.Add(entryAssembly);
            _logger.LogDebug("Added entry assembly: {AssemblyName}", entryAssembly.GetName().Name);
        }
        
        _logger.LogInformation("Found {AssemblyCount} controller assemblies", controllerAssemblies.Count);
        
        // 配置MVC
        var mvcBuilder = services.AddControllers(options =>
        {
            // 让每个模块配置MVC选项
            var modules = _moduleRegistry.GetAllModules();
            foreach (var module in modules)
            {
                try
                {
                    module.ConfigureMvc(options);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error configuring MVC for module: {ModuleId}", module.Descriptor.Id);
                    throw;
                }
            }
        });
        
        // 配置应用程序部件管理器
        mvcBuilder.ConfigureApplicationPartManager(manager =>
        {
            // 清除默认的程序集扫描
            manager.ApplicationParts.Clear();
            
            // 只添加明确指定的控制器程序集
            foreach (var assembly in controllerAssemblies)
            {
                manager.ApplicationParts.Add(new AssemblyPart(assembly));
                _logger.LogDebug("Added controller assembly: {AssemblyName}", assembly.GetName().Name);
            }
        });
        
        _logger.LogDebug("Successfully configured controllers");
    }
    
    /// <summary>
    /// 验证模块配置
    /// </summary>
    public void ValidateConfiguration()
    {
        _logger.LogInformation("Validating module configuration");
        
        var modules = _moduleRegistry.GetAllModules().ToList();
        
        // 检查依赖关系
        foreach (var module in modules)
        {
            foreach (var dependencyId in module.Descriptor.Dependencies)
            {
                if (!_moduleRegistry.IsModuleRegistered(dependencyId))
                {
                    throw new InvalidOperationException(
                        $"Module '{module.Descriptor.Id}' depends on '{dependencyId}', but it is not registered");
                }
            }
        }
        
        // 检查API客户端映射
        var allMappings = new Dictionary<string, List<string>>();
        foreach (var module in modules)
        {
            foreach (var mapping in module.Descriptor.ApiClientMappings)
            {
                if (!allMappings.ContainsKey(mapping.Key))
                {
                    allMappings[mapping.Key] = new List<string>();
                }
                allMappings[mapping.Key].Add(module.Descriptor.Id);
            }
        }
        
        // 检查重复映射
        foreach (var mapping in allMappings.Where(m => m.Value.Count > 1))
        {
            _logger.LogWarning("API client '{ApiClient}' is mapped by multiple modules: {Modules}",
                mapping.Key, string.Join(", ", mapping.Value));
        }
        
        // 检查控制器程序集
        var controllerAssemblies = _moduleRegistry.GetControllerAssemblies().ToList();
        if (!controllerAssemblies.Any())
        {
            _logger.LogWarning("No controller assemblies found in any module");
        }
        
        _logger.LogInformation("Module configuration validation completed successfully");
    }
    
    /// <summary>
    /// 获取模块统计信息
    /// </summary>
    /// <returns>模块统计信息</returns>
    public ModuleStatistics GetStatistics()
    {
        var modules = _moduleRegistry.GetAllModules().ToList();
        var controllerAssemblies = _moduleRegistry.GetControllerAssemblies().ToList();
        
        var apiClientMappings = new Dictionary<string, string>();
        foreach (var module in modules)
        {
            foreach (var mapping in module.Descriptor.ApiClientMappings)
            {
                apiClientMappings[mapping.Key] = mapping.Value;
            }
        }
        
        return new ModuleStatistics
        {
            TotalModules = modules.Count,
            ManagementModules = modules.Count(m => m.Descriptor.IsManagement),
            RegularModules = modules.Count(m => !m.Descriptor.IsManagement),
            ControllerAssemblies = controllerAssemblies.Count,
            ApiClientMappings = apiClientMappings.Count,
            ModuleDetails = modules.Select(m => new ModuleDetail
            {
                Id = m.Descriptor.Id,
                Name = m.Descriptor.Name,
                Version = m.Descriptor.Version,
                IsManagement = m.Descriptor.IsManagement,
                Dependencies = m.Descriptor.Dependencies,
                ControllerAssemblies = m.Descriptor.ControllerAssemblyMarkers.Length,
                ApiClientMappings = m.Descriptor.ApiClientMappings.Count
            }).ToList()
        };
    }
}

/// <summary>
/// 模块统计信息
/// </summary>
public class ModuleStatistics
{
    public int TotalModules { get; set; }
    public int ManagementModules { get; set; }
    public int RegularModules { get; set; }
    public int ControllerAssemblies { get; set; }
    public int ApiClientMappings { get; set; }
    public List<ModuleDetail> ModuleDetails { get; set; } = new();
}

/// <summary>
/// 模块详细信息
/// </summary>
public class ModuleDetail
{
    public string Id { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string Version { get; set; } = string.Empty;
    public bool IsManagement { get; set; }
    public string[] Dependencies { get; set; } = Array.Empty<string>();
    public int ControllerAssemblies { get; set; }
    public int ApiClientMappings { get; set; }
}
