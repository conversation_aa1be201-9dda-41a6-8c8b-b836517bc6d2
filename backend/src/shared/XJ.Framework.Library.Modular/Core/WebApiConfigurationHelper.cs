using System.Reflection;
using Microsoft.AspNetCore.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.WebApi;
using XJ.Framework.Library.WebApi.Extensions;

namespace XJ.Framework.Library.Modular.Core;

/// <summary>
/// WebApi配置辅助类
/// </summary>
public static class WebApiConfigurationHelper
{
    /// <summary>
    /// 调用常规WebApi初始化方法
    /// </summary>
    /// <typeparam name="TWebApiWrapper">WebApi包装器类型</typeparam>
    /// <typeparam name="TAuthProvider">认证提供者类型</typeparam>
    /// <typeparam name="TAuthInfoGetter">认证信息获取器类型</typeparam>
    /// <param name="builder">Web应用程序构建器</param>
    public static void InitRegularWebApi<TWebApiWrapper, TAuthProvider, TAuthInfoGetter>(
        WebApplicationBuilder builder)
        where TWebApiWrapper : WebApiWrapper, new()
        where TAuthProvider : class, IAuthProvider
        where TAuthInfoGetter : class, IAuthInfoGetter
    {
        try
        {
            builder.Init<TAuthProvider, TAuthInfoGetter, TWebApiWrapper>();

            var loggerFactory = builder.Services.BuildServiceProvider().GetService<ILoggerFactory>();
            var logger = loggerFactory?.CreateLogger("WebApiConfigurationHelper");
            logger?.LogDebug("Successfully initialized regular WebApi for {WebApiWrapper}",
                typeof(TWebApiWrapper).Name);
        }
        catch (Exception ex)
        {
            var loggerFactory = builder.Services.BuildServiceProvider().GetService<ILoggerFactory>();
            var logger = loggerFactory?.CreateLogger("WebApiConfigurationHelper");
            logger?.LogError(ex, "Failed to initialize regular WebApi for {WebApiWrapper}",
                typeof(TWebApiWrapper).Name);
            throw;
        }
    }

    /// <summary>
    /// 调用管理WebApi初始化方法
    /// </summary>
    /// <typeparam name="TWebApiMgtWrapper">WebApi管理包装器类型</typeparam>
    /// <typeparam name="TAuthProvider">认证提供者类型</typeparam>
    /// <typeparam name="TAuthInfoGetter">认证信息获取器类型</typeparam>
    /// <param name="builder">Web应用程序构建器</param>
    public static void InitManagementWebApi<TWebApiMgtWrapper, TAuthProvider, TAuthInfoGetter>(
        WebApplicationBuilder builder)
        where TWebApiMgtWrapper : WebApiMgtWrapper, new()
        where TAuthProvider : class, IAuthProvider
        where TAuthInfoGetter : class, IAuthInfoGetter
    {
        try
        {
            builder.InitMgt<TAuthProvider, TAuthInfoGetter, TWebApiMgtWrapper>();

            var loggerFactory = builder.Services.BuildServiceProvider().GetService<ILoggerFactory>();
            var logger = loggerFactory?.CreateLogger("WebApiConfigurationHelper");
            logger?.LogDebug("Successfully initialized management WebApi for {WebApiMgtWrapper}",
                typeof(TWebApiMgtWrapper).Name);
        }
        catch (Exception ex)
        {
            var loggerFactory = builder.Services.BuildServiceProvider().GetService<ILoggerFactory>();
            var logger = loggerFactory?.CreateLogger("WebApiConfigurationHelper");
            logger?.LogError(ex, "Failed to initialize management WebApi for {WebApiMgtWrapper}",
                typeof(TWebApiMgtWrapper).Name);
            throw;
        }
    }
}
