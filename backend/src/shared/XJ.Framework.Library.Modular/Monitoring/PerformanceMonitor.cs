using System.Collections.Concurrent;
using System.Diagnostics;
using Microsoft.Extensions.Logging;

namespace XJ.Framework.Library.Modular.Monitoring;

/// <summary>
/// 性能监控器
/// </summary>
public class PerformanceMonitor
{
    private readonly ConcurrentDictionary<string, CallStatistics> _statistics = new();
    private readonly ILogger<PerformanceMonitor> _logger;
    
    public PerformanceMonitor(ILogger<PerformanceMonitor> logger)
    {
        _logger = logger;
    }
    
    /// <summary>
    /// 记录API调用
    /// </summary>
    /// <param name="clientTypeName">API客户端类型名称</param>
    /// <param name="callType">调用类型</param>
    /// <param name="duration">调用耗时</param>
    /// <param name="success">是否成功</param>
    public void RecordApiCall(string clientTypeName, ApiCallType callType, TimeSpan duration, bool success = true)
    {
        var key = $"{clientTypeName}_{callType}";
        var stats = _statistics.GetOrAdd(key, _ => new CallStatistics
        {
            ClientTypeName = clientTypeName,
            CallType = callType
        });
        
        lock (stats)
        {
            stats.TotalCalls++;
            stats.TotalDuration += duration;
            
            if (success)
            {
                stats.SuccessfulCalls++;
            }
            else
            {
                stats.FailedCalls++;
            }
            
            if (duration < stats.MinDuration || stats.MinDuration == TimeSpan.Zero)
            {
                stats.MinDuration = duration;
            }
            
            if (duration > stats.MaxDuration)
            {
                stats.MaxDuration = duration;
            }
            
            stats.LastCallTime = DateTime.UtcNow;
        }
        
        // 记录慢调用
        if (duration.TotalMilliseconds > 1000) // 超过1秒的调用
        {
            _logger.LogWarning("Slow API call detected: {ClientType} ({CallType}) took {Duration}ms",
                clientTypeName, callType, duration.TotalMilliseconds);
        }
    }
    
    /// <summary>
    /// 获取所有统计信息
    /// </summary>
    /// <returns>统计信息列表</returns>
    public IEnumerable<CallStatistics> GetAllStatistics()
    {
        return _statistics.Values.Select(s => s.Clone()).ToList();
    }
    
    /// <summary>
    /// 获取特定客户端的统计信息
    /// </summary>
    /// <param name="clientTypeName">客户端类型名称</param>
    /// <returns>统计信息列表</returns>
    public IEnumerable<CallStatistics> GetStatistics(string clientTypeName)
    {
        return _statistics.Values
            .Where(s => s.ClientTypeName.Equals(clientTypeName, StringComparison.OrdinalIgnoreCase))
            .Select(s => s.Clone())
            .ToList();
    }
    
    /// <summary>
    /// 获取性能摘要
    /// </summary>
    /// <returns>性能摘要</returns>
    public PerformanceSummary GetSummary()
    {
        var allStats = _statistics.Values.ToList();
        var inMemoryStats = allStats.Where(s => s.CallType == ApiCallType.InMemory).ToList();
        var httpStats = allStats.Where(s => s.CallType == ApiCallType.Http).ToList();
        
        return new PerformanceSummary
        {
            TotalCalls = allStats.Sum(s => s.TotalCalls),
            InMemoryCalls = inMemoryStats.Sum(s => s.TotalCalls),
            HttpCalls = httpStats.Sum(s => s.TotalCalls),
            AverageInMemoryDuration = inMemoryStats.Any() 
                ? TimeSpan.FromTicks((long)inMemoryStats.Average(s => s.AverageDuration.Ticks))
                : TimeSpan.Zero,
            AverageHttpDuration = httpStats.Any()
                ? TimeSpan.FromTicks((long)httpStats.Average(s => s.AverageDuration.Ticks))
                : TimeSpan.Zero,
            SuccessRate = allStats.Sum(s => s.TotalCalls) > 0
                ? (double)allStats.Sum(s => s.SuccessfulCalls) / allStats.Sum(s => s.TotalCalls)
                : 0,
            LastUpdateTime = DateTime.UtcNow
        };
    }
    
    /// <summary>
    /// 清除统计信息
    /// </summary>
    public void ClearStatistics()
    {
        _statistics.Clear();
        _logger.LogInformation("Performance statistics cleared");
    }
    
    /// <summary>
    /// 记录性能摘要到日志
    /// </summary>
    public void LogSummary()
    {
        var summary = GetSummary();
        
        _logger.LogInformation("=== Performance Summary ===");
        _logger.LogInformation("Total Calls: {TotalCalls}", summary.TotalCalls);
        _logger.LogInformation("In-Memory Calls: {InMemoryCalls} ({InMemoryPercentage:P1})", 
            summary.InMemoryCalls, 
            summary.TotalCalls > 0 ? (double)summary.InMemoryCalls / summary.TotalCalls : 0);
        _logger.LogInformation("HTTP Calls: {HttpCalls} ({HttpPercentage:P1})", 
            summary.HttpCalls,
            summary.TotalCalls > 0 ? (double)summary.HttpCalls / summary.TotalCalls : 0);
        _logger.LogInformation("Average In-Memory Duration: {InMemoryDuration}ms", 
            summary.AverageInMemoryDuration.TotalMilliseconds);
        _logger.LogInformation("Average HTTP Duration: {HttpDuration}ms", 
            summary.AverageHttpDuration.TotalMilliseconds);
        _logger.LogInformation("Success Rate: {SuccessRate:P2}", summary.SuccessRate);
        
        if (summary.AverageHttpDuration > TimeSpan.Zero && summary.AverageInMemoryDuration > TimeSpan.Zero)
        {
            var performanceGain = summary.AverageHttpDuration.TotalMilliseconds / summary.AverageInMemoryDuration.TotalMilliseconds;
            _logger.LogInformation("Performance Gain (In-Memory vs HTTP): {PerformanceGain:F1}x faster", performanceGain);
        }
    }
}

/// <summary>
/// API调用类型
/// </summary>
public enum ApiCallType
{
    /// <summary>
    /// 内存调用
    /// </summary>
    InMemory,
    
    /// <summary>
    /// HTTP调用
    /// </summary>
    Http
}

/// <summary>
/// 调用统计信息
/// </summary>
public class CallStatistics
{
    public string ClientTypeName { get; set; } = string.Empty;
    public ApiCallType CallType { get; set; }
    public long TotalCalls { get; set; }
    public long SuccessfulCalls { get; set; }
    public long FailedCalls { get; set; }
    public TimeSpan TotalDuration { get; set; }
    public TimeSpan MinDuration { get; set; }
    public TimeSpan MaxDuration { get; set; }
    public DateTime LastCallTime { get; set; }
    
    /// <summary>
    /// 平均调用时间
    /// </summary>
    public TimeSpan AverageDuration => TotalCalls > 0 
        ? TimeSpan.FromTicks(TotalDuration.Ticks / TotalCalls) 
        : TimeSpan.Zero;
    
    /// <summary>
    /// 成功率
    /// </summary>
    public double SuccessRate => TotalCalls > 0 ? (double)SuccessfulCalls / TotalCalls : 0;
    
    /// <summary>
    /// 克隆统计信息
    /// </summary>
    /// <returns>克隆的统计信息</returns>
    public CallStatistics Clone()
    {
        return new CallStatistics
        {
            ClientTypeName = ClientTypeName,
            CallType = CallType,
            TotalCalls = TotalCalls,
            SuccessfulCalls = SuccessfulCalls,
            FailedCalls = FailedCalls,
            TotalDuration = TotalDuration,
            MinDuration = MinDuration,
            MaxDuration = MaxDuration,
            LastCallTime = LastCallTime
        };
    }
}

/// <summary>
/// 性能摘要
/// </summary>
public class PerformanceSummary
{
    public long TotalCalls { get; set; }
    public long InMemoryCalls { get; set; }
    public long HttpCalls { get; set; }
    public TimeSpan AverageInMemoryDuration { get; set; }
    public TimeSpan AverageHttpDuration { get; set; }
    public double SuccessRate { get; set; }
    public DateTime LastUpdateTime { get; set; }
    
    /// <summary>
    /// 性能提升倍数（HTTP相对于内存调用的倍数）
    /// </summary>
    public double PerformanceGain => AverageInMemoryDuration > TimeSpan.Zero && AverageHttpDuration > TimeSpan.Zero
        ? AverageHttpDuration.TotalMilliseconds / AverageInMemoryDuration.TotalMilliseconds
        : 0;
}
