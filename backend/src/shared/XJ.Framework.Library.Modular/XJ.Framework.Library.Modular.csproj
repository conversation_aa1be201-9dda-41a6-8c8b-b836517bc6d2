<Project Sdk="Microsoft.NET.Sdk">

    <Import Project="..\..\..\Common.Secrets.props"/>
    <Import Project="..\..\..\Common.props"/>

    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App" />
        <PackageReference Include="Microsoft.Extensions.DependencyInjection.Abstractions" />
        <PackageReference Include="Microsoft.Extensions.Configuration.Abstractions" />
        <PackageReference Include="Microsoft.Extensions.Logging.Abstractions" />
        <PackageReference Include="Microsoft.AspNetCore.Mvc.Core" />
        <PackageReference Include="Microsoft.AspNetCore.Routing" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\core\abstractions\XJ.Framework.Library.Common.Abstraction\XJ.Framework.Library.Common.Abstraction.csproj" />
        <ProjectReference Include="..\XJ.Framework.Library.Application.Contract\XJ.Framework.Library.Application.Contract.csproj" />
        <ProjectReference Include="..\XJ.Framework.Library.WebApi.Mgt\XJ.Framework.Library.WebApi.Mgt.csproj" />
        <ProjectReference Include="..\XJ.Framework.Library.WebApi\XJ.Framework.Library.WebApi.csproj" />
    </ItemGroup>

</Project>
