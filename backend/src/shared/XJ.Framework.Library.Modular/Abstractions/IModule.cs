using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using XJ.Framework.Library.Application.Contract.Interfaces;

namespace XJ.Framework.Library.Modular.Abstractions;

/// <summary>
/// 模块接口定义
/// </summary>
public interface IModule
{
    /// <summary>
    /// 模块描述符
    /// </summary>
    ModuleDescriptor Descriptor { get; }

    /// <summary>
    /// 配置服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    void ConfigureServices(IServiceCollection services, IConfigurationRoot configuration);

    /// <summary>
    /// 配置常规站点服务（使用WebApiWrapper）
    /// </summary>
    /// <typeparam name="TAuthProvider">认证提供者类型</typeparam>
    /// <typeparam name="TAuthInfoGetter">认证信息获取器类型</typeparam>
    /// <param name="builder">Web应用程序构建器</param>
    void ConfigureRegularWebApi<TAuthProvider, TAuthInfoGetter>(WebApplicationBuilder builder)
        where TAuthProvider : class, IAuthProvider
        where TAuthInfoGetter : class, IAuthInfoGetter;

    /// <summary>
    /// 配置管理站点服务（使用WebApiMgtWrapper）
    /// </summary>
    /// <typeparam name="TAuthProvider">认证提供者类型</typeparam>
    /// <typeparam name="TAuthInfoGetter">认证信息获取器类型</typeparam>
    /// <param name="builder">Web应用程序构建器</param>
    void ConfigureManagementWebApi<TAuthProvider, TAuthInfoGetter>(WebApplicationBuilder builder)
        where TAuthProvider : class, IAuthProvider
        where TAuthInfoGetter : class, IAuthInfoGetter;

    /// <summary>
    /// 配置应用程序
    /// </summary>
    /// <param name="app">应用程序构建器</param>
    void ConfigureApplication(WebApplication app);

    /// <summary>
    /// 配置MVC选项
    /// </summary>
    /// <param name="mvcOptions">MVC选项</param>
    void ConfigureMvc(MvcOptions mvcOptions);
}

/// <summary>
/// 模块描述符
/// </summary>
public class ModuleDescriptor
{
    /// <summary>
    /// 模块ID
    /// </summary>
    public string Id { get; set; } = string.Empty;

    /// <summary>
    /// 模块名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 模块版本
    /// </summary>
    public string Version { get; set; } = "1.0.0";

    /// <summary>
    /// 模块描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否为管理模块
    /// </summary>
    public bool IsManagement { get; set; }

    /// <summary>
    /// 控制器程序集标记类型
    /// </summary>
    public Type[] ControllerAssemblyMarkers { get; set; } = Array.Empty<Type>();

    /// <summary>
    /// 依赖的模块ID列表
    /// </summary>
    public string[] Dependencies { get; set; } = Array.Empty<string>();

    /// <summary>
    /// 路由前缀
    /// </summary>
    public string? RoutePrefix { get; set; }

    /// <summary>
    /// API客户端类型映射
    /// 键：ApiClient类型名称，值：目标模块ID
    /// </summary>
    public Dictionary<string, string> ApiClientMappings { get; set; } = new();
}

/// <summary>
/// 模块基类
/// </summary>
public abstract class ModuleBase : IModule
{
    public abstract ModuleDescriptor Descriptor { get; }

    public virtual void ConfigureServices(IServiceCollection services, IConfigurationRoot configuration)
    {
        // 默认实现为空，子类可以重写
    }

    public virtual void ConfigureRegularWebApi<TAuthProvider, TAuthInfoGetter>(WebApplicationBuilder builder)
        where TAuthProvider : class, IAuthProvider
        where TAuthInfoGetter : class, IAuthInfoGetter
    {
        // 默认实现为空，子类可以重写
        // 如果模块不支持常规站点，可以抛出异常或记录警告
    }

    public virtual void ConfigureManagementWebApi<TAuthProvider, TAuthInfoGetter>(WebApplicationBuilder builder)
        where TAuthProvider : class, IAuthProvider
        where TAuthInfoGetter : class, IAuthInfoGetter
    {
        // 默认实现为空，子类可以重写
        // 如果模块不支持管理站点，可以抛出异常或记录警告
    }

    public virtual void ConfigureApplication(WebApplication app)
    {
        // 默认实现为空，子类可以重写
    }

    public virtual void ConfigureMvc(MvcOptions mvcOptions)
    {
        // 默认实现为空，子类可以重写
    }
}
