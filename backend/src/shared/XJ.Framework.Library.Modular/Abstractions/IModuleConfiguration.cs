using Microsoft.AspNetCore.Builder;
using XJ.Framework.Library.Application.Contract.Interfaces;

namespace XJ.Framework.Library.Modular.Abstractions;

/// <summary>
/// 模块配置接口 - 支持灵活的站点配置
/// </summary>
public interface IModuleConfiguration
{
    /// <summary>
    /// 模块是否支持常规站点
    /// </summary>
    bool SupportsRegularSite { get; }

    /// <summary>
    /// 模块是否支持管理站点
    /// </summary>
    bool SupportsManagementSite { get; }
}

/// <summary>
/// 支持常规站点的模块接口
/// </summary>
/// <typeparam name="TWebApiWrapper">WebApi包装器类型</typeparam>
/// <typeparam name="TInfrastructureWrapper">基础设施包装器类型</typeparam>
public interface IRegularSiteModule<TWebApiWrapper, TInfrastructureWrapper> : IModuleConfiguration
    where TWebApiWrapper : class
    where TInfrastructureWrapper : class
{
    /// <summary>
    /// 配置常规站点
    /// </summary>
    /// <typeparam name="TAuthProvider">认证提供者类型</typeparam>
    /// <typeparam name="TAuthInfoGetter">认证信息获取器类型</typeparam>
    /// <param name="builder">Web应用程序构建器</param>
    void ConfigureRegularSite<TAuthProvider, TAuthInfoGetter>(WebApplicationBuilder builder)
        where TAuthProvider : class, IAuthProvider
        where TAuthInfoGetter : class, IAuthInfoGetter;
}

/// <summary>
/// 支持管理站点的模块接口
/// </summary>
/// <typeparam name="TWebApiMgtWrapper">WebApi管理包装器类型</typeparam>
/// <typeparam name="TInfrastructureWrapper">基础设施包装器类型</typeparam>
public interface IManagementSiteModule<TWebApiMgtWrapper, TInfrastructureWrapper> : IModuleConfiguration
    where TWebApiMgtWrapper : class
    where TInfrastructureWrapper : class
{
    /// <summary>
    /// 配置管理站点
    /// </summary>
    /// <typeparam name="TAuthProvider">认证提供者类型</typeparam>
    /// <typeparam name="TAuthInfoGetter">认证信息获取器类型</typeparam>
    /// <param name="builder">Web应用程序构建器</param>
    void ConfigureManagementSite<TAuthProvider, TAuthInfoGetter>(WebApplicationBuilder builder)
        where TAuthProvider : IAuthProvider
        where TAuthInfoGetter : IAuthInfoGetter;
}

/// <summary>
/// 同时支持常规站点和管理站点的模块接口
/// </summary>
/// <typeparam name="TWebApiWrapper">WebApi包装器类型</typeparam>
/// <typeparam name="TWebApiMgtWrapper">WebApi管理包装器类型</typeparam>
/// <typeparam name="TInfrastructureWrapper">基础设施包装器类型</typeparam>
public interface IDualSiteModule<TWebApiWrapper, TWebApiMgtWrapper, TInfrastructureWrapper> :
    IRegularSiteModule<TWebApiWrapper, TInfrastructureWrapper>,
    IManagementSiteModule<TWebApiMgtWrapper, TInfrastructureWrapper>
    where TWebApiWrapper : class
    where TWebApiMgtWrapper : class
    where TInfrastructureWrapper : class
{
}

/// <summary>
/// 模块配置基类
/// </summary>
public abstract class ModuleConfigurationBase : IModuleConfiguration
{
    /// <summary>
    /// 模块是否支持常规站点
    /// </summary>
    public virtual bool SupportsRegularSite => false;

    /// <summary>
    /// 模块是否支持管理站点
    /// </summary>
    public virtual bool SupportsManagementSite => false;
}
