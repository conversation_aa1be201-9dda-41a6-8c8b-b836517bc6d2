using System.Linq;
using System.Linq.Expressions;
using XJ.Framework.Library.Domain.Entities;
using XJ.Framework.Library.Domain.Common;

namespace XJ.Framework.Library.EntityFrameworkCore.Extensions;

public static class IQueryableExtensions
{
    public static IQueryable<TEntity> Orderby<TKey, TEntity>(this IQueryable<TEntity> queryable,
        List<OrderbyDirection<TEntity>> orderby)
        where TKey : struct
        where TEntity : BaseEntity<TKey>
    {
        if (orderby == null || orderby.Count == 0)
        {
            return queryable.OrderBy(e => e.Key);
        }

        IOrderedQueryable<TEntity> orderedQueryable = null!;
        foreach (var item in orderby)
        {
            if (orderedQueryable == null)
            {
                orderedQueryable = item.SortDirection == SortDirection.Ascending
                    ? queryable.OrderBy(item.KeySelector)
                    : queryable.OrderByDescending(item.KeySelector);
            }
            else
            {
                orderedQueryable = item.SortDirection == SortDirection.Ascending
                    ? orderedQueryable.ThenBy(item.KeySelector)
                    : orderedQueryable.ThenByDescending(item.KeySelector);
            }
        }

        return orderedQueryable ?? queryable;
    }

    // private static IOrderedQueryable<TEntity> CallOrderBy<TEntity>(IQueryable<TEntity> source,
    //     LambdaExpression keySelector, string methodName)
    // {
    //     var entityType = typeof(TEntity);
    //     var keySelectorType = keySelector.Body.Type;
    //     var method = typeof(Queryable).GetMethods()
    //         .First(m => m.Name == methodName && m.GetParameters().Length == 2)
    //         .MakeGenericMethod(entityType, keySelectorType);
    //
    //     return (IOrderedQueryable<TEntity>)method.Invoke(null, new object[] { source, keySelector });
    // }
}