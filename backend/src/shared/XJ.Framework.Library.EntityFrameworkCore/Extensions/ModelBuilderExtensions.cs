using Microsoft.EntityFrameworkCore.Storage.ValueConversion;
using System.ComponentModel.DataAnnotations.Schema;
using System.Reflection;
using XJ.Framework.Library.Common.Abstraction.Reflection;
using XJ.Framework.Library.EntityFrameworkCore.Converters;
using IndexAttribute = XJ.Framework.Library.Domain.Attributes.IndexAttribute;

namespace XJ.Framework.Library.EntityFrameworkCore.Extensions;

public static class ModelBuilderExtensions
{
    private static XmlDocumentationProvider? _xmlDocumentationProvider = null;


    public static void Initialize(this ModelBuilder modelBuilder)
    {
        var assembly = Assembly.GetCallingAssembly();

        var domainAssemblyName = assembly.GetName().Name!.Replace(".EntityFrameworkCore", ".Domain");

        var domainAssembly = Assembly.Load(domainAssemblyName);

        var xmlFilePath = Path.Combine(AppContext.BaseDirectory, $"{domainAssemblyName}.xml");

        if (File.Exists(xmlFilePath))
        {
            // throw new ValidationException($"XML documentation file not found: {xmlFilePath}");
            _xmlDocumentationProvider = new XmlDocumentationProvider(xmlFilePath);
        }


        // 自动注册当前程序集中所有的实体
        var entityTypes = domainAssembly.GetTypes()
            .Where(t => t.Name.EndsWith("Entity") && t is { IsAbstract: false, IsClass: true });

        foreach (var entityType in entityTypes)
        {
            modelBuilder.Entity(entityType).ToTable(table =>
            {
                table.HasComment(_xmlDocumentationProvider?.GetTypeDocumentation(entityType));
            });
        }
    }

    /// <summary>
    /// 为实体的所有属性添加注释
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    public static void AddPropertyComments(this ModelBuilder modelBuilder)
    {
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            var clrType = entityType.ClrType;

            // 获取所有属性，包括从基类继承的属性
            var properties = clrType.GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (var property in properties)
            {
                // 获取对应的EF Core属性
                var efProperty = entityType.FindProperty(property.Name);
                if (efProperty == null) continue;

                // 从XML文档中获取属性注释
                var propertyComment = _xmlDocumentationProvider?.GetPropertyDocumentation(property);
                if (string.IsNullOrEmpty(propertyComment)) continue;

                // 应用注释到数据库列
                modelBuilder.Entity(clrType)
                    .Property(property.Name)
                    .HasComment(propertyComment);
            }
        }
    }

    public static void ApplyIndexAttributes(this ModelBuilder modelBuilder)
    {
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            var entityTypeBuilder = modelBuilder.Entity(entityType.ClrType);
            var indexAttributes = entityType.ClrType
                .GetCustomAttributes<IndexAttribute>(inherit: true);

            foreach (var attribute in indexAttributes)
            {
                var indexBuilder = entityTypeBuilder
                    .HasIndex(attribute.PropertyNames.Select(name => name).ToArray())
                    .HasDatabaseName(attribute.Name);

                if (attribute.IsUnique)
                {
                    indexBuilder.IsUnique();
                }

                if (!string.IsNullOrEmpty(attribute.Filter))
                {
                    indexBuilder.HasFilter(attribute.Filter);
                }

                if (attribute.IsClustered)
                {
                    indexBuilder.IsClustered();
                }

                if (attribute.IncludeProperties.Length > 0)
                {
                    indexBuilder.IncludeProperties(attribute.IncludeProperties);
                }
            }
        }
    }

    /// <summary>
    /// 配置所有标记了 EnumToIntAttribute 的枚举属性转换器
    /// </summary>
    /// <param name="modelBuilder">模型构建器</param>
    public static void ConfigureEnumToIntConversions(this ModelBuilder modelBuilder)
    {
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            var properties = entityType.ClrType.GetProperties()
                .Where(p => p.PropertyType.IsEnum);

            foreach (var property in properties)
            {
                var converterType = typeof(EnumValueConverterFactory)
                    .GetMethod(nameof(EnumValueConverterFactory.Create))!
                    .MakeGenericMethod(property.PropertyType);

                var converter = converterType.Invoke(null, null);

                modelBuilder.Entity(entityType.ClrType)
                    .Property(property.Name)
                    .HasConversion((ValueConverter)converter!);
            }
        }
    }
}