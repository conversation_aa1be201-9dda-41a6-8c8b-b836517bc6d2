using System.Collections.Generic;
using System.Threading.Tasks;
using XJ.Framework.Library.Domain.Entities;

namespace XJ.Framework.Library.Domain.Repositories.Interfaces;

/// <summary>
/// 可编辑仓储接口
/// </summary>
/// <typeparam name="T<PERSON><PERSON>">主键类型</typeparam>
/// <typeparam name="TEntity">实体类型</typeparam>
public interface IEditableRepository<TKey, TEntity> : IRepository<TKey, TEntity>
    where TEntity : BaseEntity<TKey> where TKey : struct
{
    #region 插入数据

    Task<bool> InsertAsync(TEntity entity, bool isSaveChange = true);
    Task<bool> InsertAsync(List<TEntity> entities, bool isSaveChange = true);

    #endregion

    #region 删除(删除之前需要查询)

    Task<bool> DeleteAsync(TEntity entity, bool isSaveChange = true);
    Task<bool> DeleteAsync(List<TEntity> entities, bool isSaveChange = true);

    #endregion

    #region 修改数据

    Task<bool> UpdateAsync(TEntity entity, bool isSaveChange = true, List<string>? updatePropertyList = null);
    Task<bool> UpdateAsync(List<TEntity> entities, bool isSaveChange = true);

    #endregion
} 