using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace XJ.Framework.Library.Domain.Id
{
    /// <summary>
    /// IdGenerator的扩展方法
    /// </summary>
    public static class IdGeneratorExtensions
    {
        /// <summary>
        /// 初始化IdGenerator
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="workerId">工作节点ID（默认-1，表示自动获取）</param>
        /// <param name="datacenterId">数据中心ID（默认-1，表示自动获取）</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddIdGenerator(this IServiceCollection services, long workerId = -1,
            long datacenterId = -1)
        {
            // 初始化ID生成器
            IdGenerator.Initialize(workerId, datacenterId);

            return services;
        }

        /// <summary>
        /// 从配置文件初始化IdGenerator
        /// </summary>
        /// <param name="services">服务集合</param>
        /// <param name="configuration">配置对象</param>
        /// <param name="configSection">配置节点名称</param>
        /// <returns>服务集合</returns>
        public static IServiceCollection AddIdGenerator(this IServiceCollection services,
            IConfiguration configuration, string configSection = "IdGenerator")
        {
            // 从配置中读取workerId和datacenterId
            var section = configuration.GetSection(configSection);
            var workerId = section.GetValue<long?>("WorkerId") ??
                           configuration.GetValue<long?>("WorkerId") ?? -1;
            var datacenterId = section.GetValue<long?>("DatacenterId") ??
                               configuration.GetValue<long?>("DatacenterId") ?? -1;

            Console.WriteLine($"read from configuration workerId: {workerId}, datacenterId: {datacenterId}");


            // 初始化ID生成器
            IdGenerator.Initialize(workerId, datacenterId);

            // 注册IdService单例服务
            services.AddSingleton<IIdService, IdService>();

            services.AddScoped<IKeyGenerator<long>, LongKeyGenerator>();
            services.AddScoped<IKeyGenerator<Guid>, GuidKeyGenerator>();

            return services;
        }
    }

    /// <summary>
    /// ID服务接口
    /// </summary>
    public interface IIdService
    {
        /// <summary>
        /// 获取下一个ID
        /// </summary>
        /// <returns>分布式全局唯一ID</returns>
        long NextId();

        /// <summary>
        /// 解析ID信息
        /// </summary>
        /// <param name="id">ID</param>
        /// <returns>ID信息</returns>
        (long timestamp, long datacenterId, long workerId, long sequence) ParseId(long id);
    }

    /// <summary>
    /// ID服务实现
    /// </summary>
    internal class IdService : IIdService
    {
        /// <summary>
        /// 获取下一个ID
        /// </summary>
        /// <returns>分布式全局唯一ID</returns>
        public long NextId()
        {
            return IdGenerator.NextId();
        }

        /// <summary>
        /// 解析ID信息
        /// </summary>
        /// <param name="id">ID</param>
        /// <returns>ID信息</returns>
        public (long timestamp, long datacenterId, long workerId, long sequence) ParseId(long id)
        {
            return IdGenerator.ParseId(id);
        }
    }
}
