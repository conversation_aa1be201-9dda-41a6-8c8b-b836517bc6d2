using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Reflection;
using XJ.Framework.Library.Application.Contract.Interfaces;
using XJ.Framework.Library.Common.Abstraction.Configuration;
using XJ.Framework.Library.Common.Abstraction.JsonConverters;
using XJ.Framework.Library.WebApi.Options;
using XJ.Framework.Library.WebApi.Services;

namespace XJ.Framework.Library.WebApi.Extensions;

public static class WebApiMgtExtensions
{
    public static WebApplication InitMgt<TAuthProvider, TAuthInfoGetter, TWrapper>(this WebApplicationBuilder builder)
        where TWrapper : WebApiWrapper, new()
        where TAuthProvider : class, IAuthProvider
        where TAuthInfoGetter : class, IAuthInfoGetter
    {
        var wrapper = new TWrapper();


        builder.AddBasicApplication<TAuthProvider, TAuthInfoGetter, TWrapper>(wrapper);

        var app = builder.Build();

        app.UseBasicApplication(wrapper);

        return app;
    }

}