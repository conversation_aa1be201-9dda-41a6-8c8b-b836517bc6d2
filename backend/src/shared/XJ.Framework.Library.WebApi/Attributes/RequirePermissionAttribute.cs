namespace XJ.Framework.Library.WebApi.Attributes;

/// <summary>
/// 权限要求特性
/// </summary>
[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public class RequirePermissionAttribute : Attribute
{
    /// <summary>
    /// 权限代码
    /// </summary>
    public string PermissionCode { get; }

    /// <summary>
    /// 构造函数
    /// </summary>
    /// <param name="permissionCode">权限代码</param>
    public RequirePermissionAttribute(string permissionCode)
    {
        PermissionCode = permissionCode;
    }
} 