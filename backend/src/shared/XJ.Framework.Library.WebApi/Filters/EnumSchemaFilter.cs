namespace XJ.Framework.Library.WebApi.Filters;

/// <summary>
/// Swagger文档枚举字段显示枚举属性和枚举值,以及枚举描述
/// </summary>
public class EnumSchemaFilter : ISchemaFilter
{
    /// <summary>
    /// 实现接口
    /// </summary>
    /// <param name="schema"></param>
    /// <param name="context"></param>
    public void Apply(OpenApiSchema schema, SchemaFilterContext context)
    {
        if (!context.Type.IsEnum) return;

        schema.Enum.Clear();


        // var dict = new Dictionary<string, string>();

        Enum.GetNames(context.Type)
            .ToList()
            .ForEach(name =>
            {
                var enumName = name;
                schema.Enum.Add(new OpenApiString(enumName));
                //
                //
                // Enum e = (Enum)Enum.Parse(context.Type, name);
                // var enumString = "";
                //
                // var descriptionAttribute = e.GetAttribute<EnumItemDescriptionAttribute>();
                // if (descriptionAttribute != null)
                // {
                //     enumString = descriptionAttribute.Name;
                // }
                //
                // dict.Add(enumName, enumString);
            });
    }
}