using Microsoft.Extensions.DependencyInjection;
using XJ.Framework.Library.Domain.Shared.Interfaces;
using XJ.Framework.Library.WebApi.Services;

namespace XJ.Framework.Library.WebApi.Extensions;

/// <summary>
/// 服务集合扩展方法
/// </summary>
public static class ServiceCollectionExtensions
{
    /// <summary>
    /// 添加当前用户上下文服务
    /// </summary>
    public static IServiceCollection AddCurrentUserContext(this IServiceCollection services)
    {
        services.AddSingleton<ICurrentUserContext, WebApiCurrentUserContext>();
        return services;
    }

    public static IServiceCollection AddCurrentApplicationContext(this IServiceCollection services)
    {
        services.AddSingleton<ICurrentApplicationContext, WebApiApplicationContext>();
        return services;
    }
}
