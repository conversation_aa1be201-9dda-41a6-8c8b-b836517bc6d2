
using XJ.Framework.Library.Domain.Shared.Enums;

namespace XJ.Framework.Library.Domain.Shared.Dtos;

/// <summary>
/// User DTO
/// </summary>
public class UserDto : BaseDto<long>
{
    /// <summary>
    /// 用户名
    /// </summary>
    public string Username { get; set; } = null!;

    // /// <summary>
    // /// 密码哈希
    // /// </summary>
    // public string PasswordHash { get; set; } = null!;
    //
    // /// <summary>
    // /// 密码盐
    // /// </summary>
    // public string PasswordSalt { get; set; } = null!;

    /// <summary>
    /// 真实姓名
    /// </summary>
    public string RealName { get; set; } = null!;

    /// <summary>
    /// 邮箱
    /// </summary>
    public string? Email { get; set; }

    /// <summary>
    /// 手机号
    /// </summary>
    public string? Mobile { get; set; }

    /// <summary>
    /// 头像URL
    /// </summary>
    public string? Avatar { get; set; }

    /// <summary>
    /// 用户类型（1-内部用户，2-外部用户，3-系统用户）
    /// </summary>
    public UserType UserType { get; set; }

    /// <summary>
    /// 状态（1-启用，0-禁用，2-锁定）
    /// </summary>
    public UserStatus Status { get; set; }

    /// <summary>
    /// 邮箱是否已确认
    /// </summary>
    public bool EmailConfirmed { get; set; }
    /// <summary>
    /// 手机号是否已经确认
    /// </summary>
    public bool MobileConfirmed { get; set; }

    // /// <summary>
    // /// 最后登录时间
    // /// </summary>
    // public DateTime? LastLoginTime { get; set; }

    // /// <summary>
    // /// 最后登录IP
    // /// </summary>
    // public string? LastLoginIp { get; set; }

    // /// <summary>
    // /// 密码错误次数
    // /// </summary>
    // public int PasswordErrorCount { get; set; }
    //
    // /// <summary>
    // /// 密码更新时间
    // /// </summary>
    // public DateTime? PasswordUpdateTime { get; set; }

} 