// namespace XJ.Framework.Library.Domain.Shared.Models;
//
// /// <summary>
// /// 当前用户上下文模型
// /// </summary>
// public class CurrentUser
// {
//     /// <summary>
//     /// 用户ID
//     /// </summary>
//     public long Id { get; set; }
//
//     /// <summary>
//     /// 用户名
//     /// </summary>
//     public string Username { get; set; } = null!;
//
//     /// <summary>
//     /// 真实姓名
//     /// </summary>
//     public string RealName { get; set; } = null!;
//
//     /// <summary>
//     /// 邮箱
//     /// </summary>
//     public string? Email { get; set; }
//
//     /// <summary>
//     /// 用户类型
//     /// </summary>
//     public int UserType { get; set; }
//
//     /// <summary>
//     /// 状态
//     /// </summary>
//     public int Status { get; set; }
// } 