namespace XJ.Framework.Library.Application.Contract.QueryCriteria.Attributes;

public class ContainsAttribute : QueryOperatorAttribute
{
    public ContainsAttribute(string propertyName) : base(propertyName, QueryOperator.Contains)
    {
    }

    public ContainsAttribute() : base(QueryOperator.Contains)
    {
    }
}

public class EqualAttribute : QueryOperatorAttribute
{
    public EqualAttribute(string propertyName) : base(propertyName, QueryOperator.Equal)
    {
    }

    public EqualAttribute() : base(QueryOperator.Equal)
    {
    }
}

public class NotEqualAttribute : QueryOperatorAttribute
{
    public NotEqualAttribute(string propertyName) : base(propertyName, QueryOperator.NotEqual)
    {
    }

    public NotEqualAttribute() : base(QueryOperator.NotEqual)
    {
    }
}

public class StartsWithAttribute : QueryOperatorAttribute
{
    public StartsWithAttribute(string propertyName) : base(propertyName, QueryOperator.StartsWith)
    {
    }

    public StartsWithAttribute() : base(QueryOperator.StartsWith)
    {
    }
}

public class EndsWithAttribute : QueryOperatorAttribute
{
    public EndsWithAttribute(string propertyName) : base(propertyName, QueryOperator.EndsWith)
    {
    }

    public EndsWithAttribute() : base(QueryOperator.EndsWith)
    {
    }
}