using HttpMethod = System.Net.Http.HttpMethod;

namespace XJ.Framework.Library.Application.Contract.Interfaces;

public interface IAuthProvider
{
    /// <summary>
    /// 验证用户按钮权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionCode">权限代码</param>
    /// <param name="httpMethod">HTTP请求方法</param>
    /// <param name="path">请求路径</param>
    /// <param name="appCode">应用编码</param>
    /// <returns>是否有权限</returns>
    Task<bool> ValidateButtonPermissionAsync(
        string permissionCode,
        HttpMethod? httpMethod = null,
        string? path = null,
        string? appCode = null);

    /// <summary>
    /// 验证用户Api权限
    /// </summary>
    /// <param name="userId">用户ID</param>
    /// <param name="permissionCode">权限代码</param>
    /// <param name="httpMethod">HTTP请求方法</param>
    /// <param name="path">请求路径</param>
    /// <param name="appCode">应用编码</param>
    /// <returns>是否有权限</returns>
    Task<bool> ValidateApiPermissionAsync(
        string permissionCode,
        HttpMethod? httpMethod = null,
        string? path = null,
        string? appCode = null);

    /// <summary>
    /// 通过访问凭据获取用户信息
    /// </summary>
    /// <returns></returns>
    Task<UserProfileDto?> GetUserProfileAsync();
}