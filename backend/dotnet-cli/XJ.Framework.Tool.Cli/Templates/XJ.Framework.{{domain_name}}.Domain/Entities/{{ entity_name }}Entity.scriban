{{ include '_shared.scriban' }}
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace XJ.Framework.{{ domain_name }}.Domain.Entities;

/// <summary>
/// {{ entity_name }} 实体
/// </summary>
[Table("{{ table_name }}", Schema = "{{ schema }}")]
public class {{ entity_name }}Entity : {{ base_class }}<{{ key_type }}>
{
    {{~ for property in properties ~}}
    /// <summary>
    {{~ for line in property.comment | string.split '\n' ~}}
    /// {{ line }}
    {{~ end ~}}
    /// </summary>
    [Column("{{ property.original_name }}")]
    {{~ if property.type == "string" && property.max_length != null ~}}
    [StringLength({{ property.max_length }})]
    {{~ end ~}}
    public {{ if !property.nullable }}required {{ end }}{{ property.type }}{{ if property.nullable }}?{{ end }} {{ property.name }} { get; set; }{{ if !property.nullable && property.type == "string" }} = null!;{{ end }}

    {{~ end ~}}
} 