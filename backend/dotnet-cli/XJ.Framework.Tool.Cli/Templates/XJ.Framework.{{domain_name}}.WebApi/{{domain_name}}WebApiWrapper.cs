namespace XJ.Framework.{{domain_name}}.WebApi;

public class {{domain_name}}WebApiWrapper : WebApiWrapper
{
    public override void Init(IServiceCollection services, IConfigurationRoot configuration)
    {
        services
            .InitApplication<{{domain_name}}ApplicationWrapper, {{domain_name}}InfrastructureWrapper>(configuration);
        services.AddHttpClient<UserApiClient>();
    }

    public override void UseMiddleware(WebApplication app)
    {
    }
    public override void AddFilters(MvcOptions mvcOptions)
    {
        
    }
} 