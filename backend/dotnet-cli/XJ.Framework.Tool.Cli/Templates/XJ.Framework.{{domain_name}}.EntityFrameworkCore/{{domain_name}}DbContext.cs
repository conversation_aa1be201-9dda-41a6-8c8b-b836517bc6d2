namespace XJ.Framework.{{domain_name}}.EntityFrameworkCore;

public class {{domain_name}}DbContext : BaseDbContext
{
    public {{domain_name}}DbContext(DbContextOptions options, IConfiguration configuration,
        IOptions<DatabaseOption> databaseOptions) : base(options, databaseOptions)
    {
    }

    protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
    {
        {{ if db_type == "sqlserver" }}
        optionsBuilder
            .UseSqlServer(DatabaseOptions.Value["{{domain_name}}"]!.ConnectionString);
        {{ else if db_type == "postgresql" }}
        optionsBuilder
            .UseNpgsql(DatabaseOptions.Value["{{domain_name}}"]!.ConnectionString);
        {{ else if db_type == "mysql" }}
        optionsBuilder
            .UseMySql(DatabaseOptions.Value["{{domain_name}}"]!.ConnectionString, ServerVersion.AutoDetect(DatabaseOptions.Value["{{domain_name}}"]!.ConnectionString));
        {{ end }}
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder.Initialize();

        modelBuilder.AddPropertyComments();

        modelBuilder.ApplyIndexAttributes();

        modelBuilder.Seed();

        // 配置枚举转换
        modelBuilder.ConfigureEnumToIntConversions();
}

} 