{{ include '_shared.scriban' }}
namespace XJ.Framework.{{ domain_name }}.Application.Services;

/// <summary>
/// {{ entity_name }} 服务实现
/// </summary>
public sealed class {{ entity_name }}Service :
    BaseEditableAppService<{{ key_type }}, {{ entity_name }}Entity, {{ entity_name }}Dto, {{ entity_name }}OperationDto, I{{ entity_name }}Repository, {{ entity_name }}QueryCriteria>,
    {{~ if has_audit_fields || has_soft_delete ~}}
    {{~ else ~}}
    
    {{~ end ~}}
    I{{ entity_name }}Service
{
    public {{ entity_name }}Service(I{{ entity_name }}Repository repository, IMapper mapper, IUnitOfWork unitOfWork,IKeyGenerator<{{ key_type }}> keyGenerator, ICurrentUserContext currentUserContext) : base(repository, mapper, unitOfWork, keyGenerator, currentUserContext)
    {
    }
} 