
``` csharp
using Microsoft.AspNetCore.Mvc.Abstractions;
using Microsoft.AspNetCore.Mvc.Infrastructure;
using Microsoft.AspNetCore.Routing;
using XJ.Framework.Library.Common.Abstraction.Extensions;

namespace XJ.Framework.Library.Modular.ApiClients;

/// <summary>
/// 内存HTTP处理器 - 完整管道重建版本
/// </summary>
public class InMemoryHttpHandler : DelegatingHandler
{
    private readonly string _moduleId;
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<InMemoryHttpHandler> _logger;
    private RequestDelegate? _cachedPipeline;
    private readonly object _pipelineLock = new object();

    public InMemoryHttpHandler(string moduleId, IServiceProvider serviceProvider)
    {
        _moduleId = moduleId;
        _serviceProvider = serviceProvider;
        _logger = serviceProvider.GetRequiredService<ILogger<InMemoryHttpHandler>>();
    }

    public async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request)
    {
        return await SendAsync(request, CancellationToken.None);
    }

    protected override async Task<HttpResponseMessage> SendAsync(HttpRequestMessage request,
        CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Processing in-memory request for module {ModuleId}: {Method} {Uri}",
                _moduleId, request.Method, request.RequestUri);

            // 创建HTTP上下文
            var httpContext = new DefaultHttpContext();
            await SetupHttpContextAsync(httpContext, request);

            // 获取或构建管道
            var pipeline = GetOrBuildPipeline();

            // 执行管道
            await pipeline(httpContext);

            // 构建响应
            return await BuildHttpResponseAsync(httpContext);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing in-memory request for module {ModuleId}", _moduleId);
            // return new HttpResponseMessage(HttpStatusCode.InternalServerError)
            // {
            //     Content = new StringContent($"Internal server error: {ex.Message}")
            // };
            throw ex.GetRealException() ?? ex;
        }
    }

    /// <summary>
    /// 获取或构建管道
    /// </summary>
    private RequestDelegate GetOrBuildPipeline()
    {
        if (_cachedPipeline != null)
            return _cachedPipeline;

        lock (_pipelineLock)
        {
            if (_cachedPipeline != null)
                return _cachedPipeline;

            _logger.LogDebug("Building complete pipeline for module {ModuleId}", _moduleId);
            _cachedPipeline = BuildCompletePipeline();
            return _cachedPipeline;
        }
    }

    /// <summary>
    /// 构建完整的管道，包含真正的MVC控制器调用
    /// </summary>
    private RequestDelegate BuildCompletePipeline()
    {
        _logger.LogDebug("Building complete pipeline with MVC support for module {ModuleId}", _moduleId);

        return async context =>
        {
            try
            {
                // 添加模块路由前缀
                var moduleManager = _serviceProvider.GetService<ModuleManager>();
                if (moduleManager != null)
                {
                    var routePrefix = moduleManager.GetModuleRoutePrefix(_moduleId);
                    if (!string.IsNullOrEmpty(routePrefix))
                    {
                        var originalPath = context.Request.Path.Value;
                        var newPath = $"/{routePrefix.TrimStart('/')}{originalPath}";
                        context.Request.Path = newPath;
                        _logger.LogDebug(
                            "Updated request path from {OriginalPath} to {NewPath} using module route prefix: {RoutePrefix}",
                            originalPath, newPath, routePrefix);
                    }
                }

                // 创建服务作用域
                var scope = _serviceProvider.CreateScope();
                context.RequestServices = scope.ServiceProvider;

                // 尝试通过MVC处理请求
                var processed = await TryProcessThroughMvcAsync(context);
                if (!processed)
                {
                    // 如果MVC处理失败，返回404
                    context.Response.StatusCode = 404;
                    context.Response.ContentType = "application/json";

                    var errorResponse = new
                    {
                        error = "Not Found",
                        message = $"No matching controller action found for path: {context.Request.Path}",
                        moduleId = _moduleId,
                        timestamp = DateTime.UtcNow
                    };

                    var json = System.Text.Json.JsonSerializer.Serialize(errorResponse);
                    await context.Response.WriteAsync(json);
                }

                _logger.LogDebug("Request processed through complete pipeline for module {ModuleId}", _moduleId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in complete pipeline for module {ModuleId}", _moduleId);
                throw ex.GetRealException() ?? ex;
                // context.Response.StatusCode = 500;
                // context.Response.ContentType = "application/json";
                //
                // var errorResponse = new
                // {
                //     error = "Internal Server Error",
                //     message = ex.Message,
                //     moduleId = _moduleId,
                //     timestamp = DateTime.UtcNow
                // };

                // var json = System.Text.Json.JsonSerializer.Serialize(errorResponse);
                // await context.Response.WriteAsync(json);
            }
        };
    }

    /// <summary>
    /// 尝试通过MVC处理请求
    /// </summary>
    private async Task<bool> TryProcessThroughMvcAsync(HttpContext context)
    {
        try
        {
            _logger.LogDebug("Attempting to process request through MVC for path: {Path}", context.Request.Path);

            // 获取MVC相关服务
            var actionDescriptorCollectionProvider = _serviceProvider.GetService<IActionDescriptorCollectionProvider>();
            var actionInvokerFactory = _serviceProvider.GetService<IActionInvokerFactory>();

            if (actionDescriptorCollectionProvider == null || actionInvokerFactory == null)
            {
                _logger.LogWarning("MVC services not available for module {ModuleId}", _moduleId);
                return false;
            }

            // 获取所有可用的Action描述符
            var actionDescriptors = actionDescriptorCollectionProvider.ActionDescriptors.Items;
            _logger.LogDebug("Found {Count} total action descriptors", actionDescriptors.Count);

            // 手动匹配路径
            var matchingAction = FindMatchingAction(actionDescriptors, context.Request.Path, context.Request.Method);
            if (matchingAction == null)
            {
                _logger.LogDebug("No matching action found for path: {Path} and method: {Method}", context.Request.Path,
                    context.Request.Method);
                return false;
            }

            _logger.LogDebug("Found matching action: {DisplayName}", matchingAction.DisplayName);

            // 创建路由数据
            var routeData = new RouteData();
            ExtractRouteValues(matchingAction, context.Request.Path, routeData);

            // 创建Action上下文
            var actionContext = new ActionContext(context, routeData, matchingAction);

            // 创建并执行Action Invoker
            var invoker = actionInvokerFactory.CreateInvoker(actionContext);
            if (invoker != null)
            {
                await invoker.InvokeAsync();
                _logger.LogDebug("Action invoked successfully for path: {Path}", context.Request.Path);
                return true;
            }
            else
            {
                _logger.LogWarning("Failed to create action invoker for path: {Path}", context.Request.Path);
                return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing request through MVC for module {ModuleId}", _moduleId);
            throw ex.GetRealException() ?? ex;
        }
    }

    /// <summary>
    /// 查找匹配的Action描述符
    /// </summary>
    private ActionDescriptor? FindMatchingAction(IReadOnlyList<ActionDescriptor> actionDescriptors, string path,
        string method)
    {
        _logger.LogDebug("Searching for action matching path: {Path} and method: {Method}", path, method);

        foreach (var action in actionDescriptors)
        {
            // 检查HTTP方法匹配
            if (!IsHttpMethodMatch(action, method))
                continue;

            // 检查路径匹配
            if (IsPathMatch(action, path))
            {
                _logger.LogDebug("Found matching action: {ActionName} with route template: {RouteTemplate}",
                    action.DisplayName, GetRouteTemplate(action));
                return action;
            }
        }

        _logger.LogDebug("No matching action found for path: {Path}", path);
        return null;
    }

    /// <summary>
    /// 检查HTTP方法是否匹配
    /// </summary>
    private bool IsHttpMethodMatch(ActionDescriptor action, string method)
    {
        var httpMethodMetadata = action.EndpointMetadata?.OfType<Microsoft.AspNetCore.Mvc.Routing.HttpMethodAttribute>()
            .FirstOrDefault();
        if (httpMethodMetadata == null)
            return true; // 如果没有指定HTTP方法，则匹配所有方法

        return httpMethodMetadata.HttpMethods.Contains(method, StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// 检查路径是否匹配
    /// </summary>
    private bool IsPathMatch(ActionDescriptor action, string path)
    {
        var routeTemplate = GetRouteTemplate(action);
        if (string.IsNullOrEmpty(routeTemplate))
            return false;

        _logger.LogDebug("Checking path match: {Path} against template: {Template}", path, routeTemplate);

        // 简单的路径匹配逻辑
        // 将路由模板转换为正则表达式进行匹配
        var pattern = ConvertRouteTemplateToRegex(routeTemplate);
        var isMatch = System.Text.RegularExpressions.Regex.IsMatch(path, pattern,
            System.Text.RegularExpressions.RegexOptions.IgnoreCase);

        _logger.LogDebug("Pattern: {Pattern}, IsMatch: {IsMatch}", pattern, isMatch);

        return isMatch;
    }

    /// <summary>
    /// 获取路由模板
    /// </summary>
    private string GetRouteTemplate(ActionDescriptor action)
    {
        // 首先尝试从AttributeRouteInfo获取完整的路由模板
        if (action.AttributeRouteInfo?.Template != null)
        {
            var template = action.AttributeRouteInfo.Template;
            _logger.LogDebug("Found AttributeRouteInfo template: {Template}", template);
            return template;
        }

        // 如果没有AttributeRouteInfo，尝试从Route属性获取
        var routeAttribute =
            action.EndpointMetadata?.OfType<Microsoft.AspNetCore.Mvc.RouteAttribute>().FirstOrDefault();
        if (routeAttribute != null)
        {
            var template = routeAttribute.Template ?? "";
            _logger.LogDebug("Found Route attribute template: {Template}", template);
            return template;
        }

        // 如果都没有，尝试组合控制器和方法的路由信息
        var controllerName = "";
        var actionName = "";
        var httpMethodTemplate = "";

        // 从RouteValues获取控制器和方法名
        if (action.RouteValues.TryGetValue("controller", out var controller))
            controllerName = controller;
        if (action.RouteValues.TryGetValue("action", out var actionValue))
            actionName = actionValue;

        // 从HTTP方法属性获取路由模板
        var httpGetAttribute = action.EndpointMetadata?.OfType<Microsoft.AspNetCore.Mvc.HttpGetAttribute>()
            .FirstOrDefault();
        if (httpGetAttribute != null && !string.IsNullOrEmpty(httpGetAttribute.Template))
        {
            httpMethodTemplate = httpGetAttribute.Template;
        }

        // 组合路由模板
        var combinedTemplate = "";
        if (!string.IsNullOrEmpty(controllerName))
        {
            combinedTemplate = $"[controller]";
            if (!string.IsNullOrEmpty(httpMethodTemplate))
            {
                combinedTemplate += "/" + httpMethodTemplate;
            }
        }

        _logger.LogDebug(
            "Combined route template: {Template} (controller: {Controller}, action: {Action}, httpTemplate: {HttpTemplate})",
            combinedTemplate, controllerName, actionName, httpMethodTemplate);

        return combinedTemplate;
    }

    /// <summary>
    /// 将路由模板转换为正则表达式
    /// </summary>
    private string ConvertRouteTemplateToRegex(string routeTemplate)
    {
        // 简化的路由模板转换
        // 例如：api/dynamic-form/[controller]/definition/{formCode} -> ^/api/dynamic-form/Form/definition/([^/]+)$
        var pattern = routeTemplate;

        // 使用正则表达式替换参数占位符
        pattern = System.Text.RegularExpressions.Regex.Replace(pattern, @"\{[^}]+\}", "([^/]+)");

        // 替换控制器和动作占位符
        pattern = pattern.Replace("[controller]", "Form"); // 将 [controller] 替换为具体的控制器名称
        pattern = pattern.Replace("[action]", "[^/]+");

        // 确保以 / 开头
        if (!pattern.StartsWith("/"))
            pattern = "/" + pattern;

        // 添加正则表达式的开始和结束标记
        var regex = "^" + pattern + "$";

        _logger.LogDebug("Converted route template '{Template}' to regex pattern '{Pattern}'", routeTemplate, regex);

        return regex;
    }

    /// <summary>
    /// 从路径中提取路由值
    /// </summary>
    private void ExtractRouteValues(ActionDescriptor action, string path, RouteData routeData)
    {
        var routeTemplate = GetRouteTemplate(action);
        if (string.IsNullOrEmpty(routeTemplate))
            return;

        _logger.LogDebug("Extracting route values from path: {Path} using template: {Template}", path, routeTemplate);

        // 从Action中获取控制器和方法名
        if (action.RouteValues.TryGetValue("controller", out var controller))
            routeData.Values["controller"] = controller;
        if (action.RouteValues.TryGetValue("action", out var actionValue))
            routeData.Values["action"] = actionValue;

        // 提取路由参数
        var parameters = ExtractRouteParameters(routeTemplate, path);
        foreach (var parameter in parameters)
        {
            routeData.Values[parameter.Key] = parameter.Value;
            _logger.LogDebug("Extracted route parameter: {Key} = {Value}", parameter.Key, parameter.Value);
        }
    }

    /// <summary>
    /// 从路由模板和路径中提取参数
    /// </summary>
    private Dictionary<string, object> ExtractRouteParameters(string routeTemplate, string path)
    {
        var parameters = new Dictionary<string, object>();

        try
        {
            // 找到路由模板中的参数占位符
            var parameterMatches =
                System.Text.RegularExpressions.Regex.Matches(routeTemplate, @"\{([^}:]+)(?::[^}]+)?\}");
            var parameterNames = new List<string>();

            foreach (System.Text.RegularExpressions.Match paramMatch in parameterMatches)
            {
                parameterNames.Add(paramMatch.Groups[1].Value);
            }

            if (parameterNames.Count == 0)
                return parameters;

            // 将路由模板转换为正则表达式
            var pattern = routeTemplate;
            foreach (var paramName in parameterNames)
            {
                // 替换参数占位符为捕获组
                pattern = System.Text.RegularExpressions.Regex.Replace(pattern, @"\{" + paramName + @"(?::[^}]+)?\}",
                    "([^/]+)");
            }

            // 确保模式匹配完整路径
            if (!pattern.StartsWith("/"))
                pattern = "/" + pattern;
            if (!pattern.StartsWith("^"))
                pattern = "^" + pattern;
            if (!pattern.EndsWith("$"))
                pattern = pattern + "$";

            _logger.LogDebug("Route parameter extraction pattern: {Pattern}", pattern);

            // 使用正则表达式匹配路径
            var regex = new System.Text.RegularExpressions.Regex(pattern,
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);
            var match = regex.Match(path);

            if (match.Success)
            {
                for (int i = 0; i < parameterNames.Count && i + 1 < match.Groups.Count; i++)
                {
                    var paramName = parameterNames[i];
                    var paramValue = match.Groups[i + 1].Value;

                    // URL解码参数值
                    paramValue = Uri.UnescapeDataString(paramValue);

                    parameters[paramName] = paramValue;
                }
            }
            else
            {
                _logger.LogWarning("Failed to match path {Path} against pattern {Pattern}", path, pattern);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting route parameters from template {Template} and path {Path}",
                routeTemplate, path);
        }

        return parameters;
    }


    /// <summary>
    /// 设置HTTP上下文
    /// </summary>
    private async Task SetupHttpContextAsync(HttpContext httpContext, HttpRequestMessage request)
    {
        httpContext.Request.Method = request.Method.Method;
        httpContext.Request.Path = request.RequestUri?.AbsolutePath ?? "/";
        httpContext.Request.QueryString = new QueryString(request.RequestUri?.Query ?? "");

        // 设置请求体
        if (request.Content != null)
        {
            var contentStream = await request.Content.ReadAsStreamAsync();
            httpContext.Request.Body = contentStream;

            var contentType = request.Content.Headers.ContentType?.ToString();
            if (!string.IsNullOrEmpty(contentType))
            {
                httpContext.Request.ContentType = contentType;
            }
        }

        // 复制请求头
        foreach (var header in request.Headers)
        {
            httpContext.Request.Headers[header.Key] = header.Value.ToArray();
        }

        // 复制内容头
        if (request.Content?.Headers != null)
        {
            foreach (var header in request.Content.Headers)
            {
                httpContext.Request.Headers[header.Key] = header.Value.ToArray();
            }
        }

        // 设置响应流
        var responseStream = new MemoryStream();
        httpContext.Response.Body = responseStream;
    }

    /// <summary>
    /// 构建HTTP响应
    /// </summary>
    private async Task<HttpResponseMessage> BuildHttpResponseAsync(HttpContext httpContext)
    {
        var responseStream = (MemoryStream)httpContext.Response.Body;
        responseStream.Position = 0;

        var response = new HttpResponseMessage((HttpStatusCode)httpContext.Response.StatusCode);

        // 设置响应内容
        if (responseStream.Length > 0)
        {
            var responseContent = new byte[responseStream.Length];
            await responseStream.ReadExactlyAsync(responseContent, 0, (int)responseStream.Length);
            response.Content = new ByteArrayContent(responseContent);
        }

        // 复制响应头
        foreach (var header in httpContext.Response.Headers)
        {
            if (header.Key.Equals("Content-Type", StringComparison.OrdinalIgnoreCase))
            {
                response.Content?.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray());
            }
            else
            {
                response.Headers.TryAddWithoutValidation(header.Key, header.Value.ToArray());
            }
        }

        return response;
    }

    /// <summary>
    /// 内存调用的WebApiWrapper
    /// </summary>
    private class InMemoryWebApiWrapper : WebApiWrapper
    {
        private readonly string _moduleId;
        private readonly IServiceProvider _originalServiceProvider;

        public InMemoryWebApiWrapper(string moduleId, IServiceProvider originalServiceProvider)
        {
            _moduleId = moduleId;
            _originalServiceProvider = originalServiceProvider;
        }

        public override void Init(IServiceCollection services, IConfigurationRoot configuration)
        {
            // 模块特定的服务配置
        }

        public override void UseMiddleware(WebApplication app)
        {
            // 模块特定的中间件配置
        }

        public override void AddFilters(Microsoft.AspNetCore.Mvc.MvcOptions mvcOptions)
        {
            // 模块特定的过滤器配置
        }
    }
}

```
